// @ts-nocheck
export const injectThemeStyle = (themeStyle: {
  [cssVar: string]: string;
}): void => {
  if (typeof window === "undefined") {
    const themeStyleArray = Object.entries(themeStyle ?? {}).reduce(
      (acc, [cssVariable, value]) => {
        return [...acc, `${cssVariable}: ${value}`];
      },
      []
    );

    const themeStyleIndex = globalThis["__styleInject_SSR_MODULES"]?.findIndex(
      (globalStyles: { id: string; cssCode: string }) => {
        return globalStyles?.id === "themeStyles";
      }
    );

    const cssCode = `:root{${themeStyleArray.join("; ")}}`;

    if (themeStyleIndex > 0) {
      globalThis["__styleInject_SSR_MODULES"][themeStyleIndex].cssCode =
        cssCode;
    } else {
      globalThis["__styleInject_SSR_MODULES"] = [].concat(
        globalThis["__styleInject_SSR_MODULES"],
        {
          id: "themeStyles",
          cssCode,
        }
      );
    }
  }
};
