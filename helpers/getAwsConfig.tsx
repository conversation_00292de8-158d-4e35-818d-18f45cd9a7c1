import { hzuAwsConfig } from "@/configs/hzu";
import { limAwsConfig } from "@/configs/lim";
import { ucwAwsConfig } from "@/configs/ucw";
import { uegAwsConfig } from "@/configs/ueg";
import { unfcAwsConfig } from "@/configs/unfc";
export const getAwsConfiguration = (): any => {
  const oapName = process.env.NEXT_PUBLIC_OAP_NAME?.toLowerCase();
  switch (oapName) {
    case "hzu":
      return hzuAwsConfig;
    case "lim":
      return limAwsConfig;
    case "ucw":
      return ucwAwsConfig;
    case "unfc":
      return unfcAwsConfig;
    case "ueg":
      return uegAwsConfig;
    default:
      return limAwsConfig;
  }
};
