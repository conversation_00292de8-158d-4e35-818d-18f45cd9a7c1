// @ts-nocheck
import React from "react";
import DOMPurify from "dompurify";
import { J<PERSON><PERSON> } from "jsdom";

interface SSRInjectStylesProps {
  id: string;
  cssCode: string;
}

const SSRInjectStyles = () => {
  const SSR_INJECT_ID = "__styleInject_SSR_MODULES";
  if (!globalThis?.[SSR_INJECT_ID]) return null;

  const styles: [SSRInjectStylesProps] = globalThis[SSR_INJECT_ID];

  const uniqueStyles = styles.reduce<Array<SSRInjectStylesProps>>(
    (acc, curr) => {
      if (!acc.find((style) => style?.id === curr?.id)) {
        acc.push(curr);
      }
      return acc;
    },
    []
  );

  const ssrDom = DOMPurify(new JSDOM("<!DOCTYPE html>").window);

  return (
    <>
      {uniqueStyles.map((module) => {
        return (
          <style
            id={module?.id}
            key={module?.id}
            dangerouslySetInnerHTML={{
              // sanitize css code, to avoid dangerouslySetInnerHTML since code is unknown and prevent XSS attacks
              __html: ssrDom.sanitize(module?.cssCode),
            }}
          />
        );
      })}
    </>
  );
};

SSRInjectStyles.displayName = "SSRInjectStyles";
export { SSRInjectStyles };
