/**
 * Skip Links Demo Component
 * 
 * This component demonstrates how to test skip links functionality.
 * Use this for development and testing purposes.
 */

"use client";

import React from "react";

export const SkipLinksDemo: React.FC = () => {
  if (process.env.NODE_ENV !== "development") {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-gray-800 text-white p-4 rounded-lg shadow-lg z-50 max-w-sm">
      <h3 className="font-bold mb-2">Skip Links Testing</h3>
      <div className="text-sm space-y-2">
        <p>
          <strong>To test skip links:</strong>
        </p>
        <ol className="list-decimal list-inside space-y-1 text-xs">
          <li>Press Tab key to focus the skip link</li>
          <li>Press Enter or Space to activate it</li>
          <li>Focus should move to main content</li>
          <li>Skip link should be visible when focused</li>
        </ol>
        <p className="text-xs text-gray-300 mt-2">
          Skip links follow WebAIM guidelines for accessibility.
        </p>
      </div>
    </div>
  );
};

export default SkipLinksDemo;
