"use client";

import React from "react";

/**
 * Skip Links Component - WebAIM Compliant Implementation
 *
 * This component implements skip navigation links according to WebAIM guidelines:
 * https://webaim.org/techniques/skipnav/#visible
 *
 * Key Features:
 * - Hidden by default, visible on keyboard focus (WebAIM recommended approach)
 * - Positioned off-screen using CSS (not display:none which removes from tab order)
 * - Becomes prominently visible when focused with smooth animations
 * - <PERSON><PERSON><PERSON> manages focus when activated
 * - Uses "Skip to main content" wording as recommended by WebAIM
 * - Single skip link approach (multiple skip links usually unnecessary)
 * - High contrast mode support
 * - Proper ARIA labeling and semantic HTML
 *
 * The skip link appears at the very top of the tab order and allows keyboard
 * and screen reader users to bypass navigation and jump directly to main content.
 */

interface SkipLink {
  href: string;
  label: string;
}

interface SkipLinksProps {
  links?: SkipLink[];
  className?: string;
}

const defaultLinks: SkipLink[] = [
  { href: "#maincontent", label: "Skip to main content" },
];

export const SkipLinks: React.FC<SkipLinksProps> = ({
  links = defaultLinks,
  className = "",
}) => {
  const handleSkipLinkClick = (
    event:
      | React.MouseEvent<HTMLAnchorElement>
      | React.KeyboardEvent<HTMLAnchorElement>
  ) => {
    const href = event.currentTarget.getAttribute("href");
    if (!href) return;

    event.preventDefault();

    // Find the target element
    const targetElement = document.querySelector(href) as HTMLElement;
    if (targetElement) {
      // WebAIM recommendation: Make the target focusable if it isn't already
      const originalTabIndex = targetElement.getAttribute("tabindex");
      const isNaturallyFocusable = targetElement.matches(
        'a[href], button, input, textarea, select, details, [tabindex]:not([tabindex="-1"])'
      );

      // If the target isn't naturally focusable, make it focusable temporarily
      if (!isNaturallyFocusable && originalTabIndex === null) {
        targetElement.setAttribute("tabindex", "-1");
      }

      // Scroll to the target element
      targetElement.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });

      // Focus the target element
      // Use a small delay to ensure scrolling completes
      setTimeout(() => {
        targetElement.focus();

        // Add a visual indicator that focus has moved (for debugging)
        if (process.env.NODE_ENV === "development") {
          console.log("Skip link activated, focus moved to:", targetElement);
        }
      }, 100);

      // Clean up temporary tabindex after a reasonable delay
      if (!isNaturallyFocusable && originalTabIndex === null) {
        setTimeout(() => {
          targetElement.removeAttribute("tabindex");
        }, 1000);
      }
    } else {
      console.warn(`Skip link target "${href}" not found`);
    }
  };

  return (
    <>
      {links.map((link, index) => (
        <a
          key={index}
          href={link.href}
          className={`skip-links ${className}`.trim()}
          onClick={handleSkipLinkClick}
          onKeyDown={(e) => {
            if (e.key === "Enter" || e.key === " ") {
              e.preventDefault();
              handleSkipLinkClick(e);
            }
          }}
        >
          {link.label}
        </a>
      ))}
    </>
  );
};

export default SkipLinks;
