import { cn } from "@/lib/utils";
import React, { useState } from "react";

export function PasswordInput({
  label = "Password",
  id = `password-${label}`,
  placeholder = "Enter your password",
  className,
  ...props
}: any) {
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);

  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  return (
    <div className="grid w-full  items-center gap-1.5">
      <div className="relative">
        <input
          {...props}
          type={isPasswordVisible ? "text" : "password"}
          id={id}
          placeholder={placeholder}
          className={cn(
            "flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-border focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",
            className
          )}
          aria-label={placeholder}
        />
        <button
          type="button"
          onClick={togglePasswordVisibility}
          className="absolute inset-y-0 right-0 px-3 flex items-center focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 rounded-sm transition-colors hover:bg-gray-100"
          aria-label="Toggle password visibility"
        >
          {isPasswordVisible ? (
            <svg
              width="18px"
              height="18px"
              viewBox="0 -1 17 17"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M7.00012 7.5C7.00012 6.67157 7.67169 6 8.50012 6C9.32855 6 10.0001 6.67157 10.0001 7.5C10.0001 8.32843 9.32855 9 8.50012 9C7.67169 9 7.00012 8.32843 7.00012 7.5Z"
                fill="#c5c5c5"
              />
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M15.5001 7.49998C15.9643 7.3143 15.9642 7.31409 15.9641 7.31385L15.9633 7.31182L15.9616 7.30752L15.9558 7.29356C15.951 7.28192 15.9441 7.26564 15.9352 7.24504C15.9173 7.20385 15.8912 7.14533 15.8566 7.07195C15.7875 6.92526 15.6841 6.71871 15.5444 6.47224C15.2657 5.98029 14.8391 5.32375 14.2467 4.66552C13.0612 3.34827 11.186 2 8.50005 2C5.81406 2 3.93893 3.34827 2.75341 4.66552C2.161 5.32375 1.73445 5.98029 1.45567 6.47224C1.31601 6.71872 1.21266 6.92526 1.1435 7.07195C1.10891 7.14533 1.08282 7.20386 1.06497 7.24505C1.05604 7.26564 1.04917 7.28192 1.04433 7.29356L1.03857 7.30752L1.03682 7.31182L1.03623 7.31329C1.03614 7.31353 1.03582 7.31431 1.50006 7.5L1.03582 7.31431L0.961548 7.5L1.03582 7.68569L1.50006 7.5C1.03582 7.68569 1.03573 7.68546 1.03582 7.68569L1.03623 7.68671L1.03682 7.68818L1.03857 7.69248L1.04433 7.70644C1.04917 7.71808 1.05604 7.73436 1.06497 7.75495C1.08282 7.79614 1.10891 7.85467 1.1435 7.92805C1.21266 8.07474 1.31601 8.28128 1.45567 8.52776C1.73445 9.01971 2.161 9.67625 2.75341 10.3345C3.93893 11.6517 5.81406 13 8.50005 13C11.186 13 13.0612 11.6517 14.2467 10.3345C14.8391 9.67625 15.2657 9.01971 15.5444 8.52776C15.6841 8.28129 15.7875 8.07474 15.8566 7.92805C15.8912 7.85467 15.9173 7.79615 15.9352 7.75496C15.9441 7.73436 15.951 7.71808 15.9558 7.70644L15.9616 7.69248L15.9633 7.68818L15.9639 7.68671C15.964 7.68647 15.9643 7.68566 15.5001 7.49998L15.9643 7.6857L16.0386 7.5L15.9641 7.31385L15.5001 7.49998ZM8.50012 5C7.11941 5 6.00012 6.11929 6.00012 7.5C6.00012 8.88071 7.11941 10 8.50012 10C9.88083 10 11.0001 8.88071 11.0001 7.5C11.0001 6.11929 9.88083 5 8.50012 5Z"
                fill="#c5c5c5"
              />
            </svg>
          ) : (
            <svg
              width="18px"
              height="18px"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M20.707 20.707a1 1 0 0 0 0-1.414l-16-16a1 1 0 0 0-1.414 1.414L5.205 6.62C2.785 8.338 1.5 10.683 1.5 12c0 2.25 3.75 7.5 10.5 7.5 1.916 0 3.59-.423 5.006-1.08l2.287 2.287a1 1 0 0 0 1.414 0zm-6.13-4.716-1.51-1.51a2.7 2.7 0 0 1-3.548-3.548l-1.51-1.51a4.75 4.75 0 0 0 6.568 6.568zM22.5 12c0 1.005-.749 2.61-2.18 4.078l-3.594-3.595a4.75 4.75 0 0 0-5.209-5.209L9.088 4.846C9.985 4.626 10.957 4.5 12 4.5c6.75 0 10.5 5.25 10.5 7.5z"
                fill="#c5c5c5"
              />
            </svg>
          )}
        </button>
      </div>
    </div>
  );
}
