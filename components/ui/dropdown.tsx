import Select, { createFilter } from "react-select";
import { useEffect } from "react";
import { useAtom } from "jotai";
import { preferredLanguage } from "@/lib/atom";

interface DropDownProps {
  selectedValue: any;
  placeholder: string;
  items: Array<any>;
  onChange: Function;
  register?: any;
  onBlur?: Function;
  isLoading?: boolean | undefined;
  required?: boolean;
  isDisabled?: boolean;
  errorMessage?: string;
  isMulti?: boolean;
  onFocus?: any;
  setError?: any;
  watch?: any;
  clearErrors?: any;
  validateWith?: any;
  fieldName?: any;
  validateWithEnd?: any;
  validateWithStart?: any;
  validationMessageForStart?: any;
  validationMessageForEnd?: any;
  noOptionsMessage?: string;
  subSectionIndex?: any;
  validateWithFieldName?: any;
  subsectionName?: any;
  isFilteredField?: boolean;
  selectedAnswers?: any;
  filterRules?: any;
  studentData?: any;
}

export default function DropDownBox({
  selectedValue,
  placeholder,
  items,
  onChange,
  register,
  onBlur,
  isLoading,
  validateWithEnd,
  validateWithStart,
  validationMessageForEnd,
  validationMessageForStart,
  watch,
  setError,
  required,
  isDisabled,
  errorMessage,
  isMulti,
  onFocus,
  fieldName,
  noOptionsMessage,
  validateWithFieldName,
  subSectionIndex,
  subsectionName,
  isFilteredField,
  selectedAnswers,
  filterRules,
  studentData,
}: DropDownProps) {
  let itemsArray = Array.isArray(items) ? items : [];

  const [preferLang] = useAtom(preferredLanguage);

  const customFilterOption = createFilter({
    matchFrom: "start",
    ignoreCase: true,
  });

  useEffect(() => {
    if (validateWithStart) {
      const validateValueWith = watch(
        fieldName.replace(/^((?:[^.]*\.){2})[^.]+/, `$1${validateWithStart}`)
      );

      const endYearValue = parseInt(watch(fieldName), 10);

      if (endYearValue < validateValueWith) {
        setError(fieldName, {
          type: "custom",
          message: validationMessageForEnd,
        });
      }
    } else if (validateWithEnd) {
      const validateValueWith = watch(
        fieldName.replace(/^((?:[^.]*\.){2})[^.]+/, `$1${validateWithEnd}`)
      );

      const startYearValue = parseInt(watch(fieldName), 10);

      if (startYearValue > validateValueWith) {
        setError(fieldName, {
          type: "custom",
          message: validationMessageForStart,
        });
      }
    }
  }, [validateWithStart, selectedValue, fieldName, validateWithEnd]);

  const getFilterOptions = (items: any) => {
    if (!filterRules) return items;

    if (selectedAnswers) {
      for (const rule of filterRules) {
        const matches = Object.entries(rule.conditions).every(
          ([key, value]) => selectedAnswers[key] === value
        );

        if (matches) {
          return items.filter((item: any) =>
            rule.allowedLabels.includes(item.label)
          );
        }
      }
    }

    if (studentData) {
      for (const rule of filterRules) {
        const { conditions, allowedLabels } = rule;
        const { fieldName: conditionField, value: conditionValue } = conditions;

        if (studentData[conditionField] === conditionValue) {
          return items.filter((item: any) =>
            allowedLabels.includes(item.label)
          );
        }
      }
    }

    return items;
  };

  const updatedOptions = isFilteredField
    ? getFilterOptions(items)
    : items || [];

  return (
    <Select
      required={required}
      options={
        preferLang === "de"
          ? (Array.isArray(updatedOptions) ? updatedOptions : []).map(
              (item: any) => ({
                value: item.value,
                label: item.displayText || item.label,
                originalItem: item,
              })
            )
          : Array.isArray(updatedOptions)
          ? updatedOptions
          : []
      }
      className=""
      placeholder={placeholder}
      {...register}
      filterOption={customFilterOption}
      menuPlacement="auto"
      isLoading={isLoading}
      aria-label={placeholder || "Select an option"}
      classNamePrefix="react-select"
      styles={{
        option: (provided: any, state: any) => ({
          ...provided,
          backgroundColor: state.isSelected ? "#0B5CD7" : "",
          color: state.isSelected ? "#FFFFFF" : "#0A0A0A",
        }),
        placeholder: (provided: any) => ({
          ...provided,
          color: "var(--color-border)",
        }),
        control: (provided: any) => ({
          ...provided,
          borderColor: errorMessage
            ? "var(--color-error)"
            : "var(--color-border)",
          "&:hover": {
            borderColor: "#0B5CD7",
          },
          "&:focus": {
            borderColor: "#0B5CD7",
          },
        }),
        menu: (base: any) => ({
          ...base,
          zIndex: 100,
          flex: 1,
        }),
      }}
      onBlur={onBlur}
      isOptionSelected={(option: any) => {
        if (preferLang === "de") {
          return (
            option.value === (selectedValue?.value || selectedValue) ||
            option.label ===
              (selectedValue?.displayText || selectedValue?.label)
          );
        }
        return (
          (option.value === selectedValue?.value &&
            option.label === selectedValue?.label) ||
          option.value === selectedValue
        );
      }}
      value={
        preferLang === "de"
          ? itemsArray
              .map((item: any) => ({
                value: item.value,
                label: item.displayText || item.label,
                originalItem: item,
              }))
              .find(
                (item: any) =>
                  item.value === (selectedValue?.value || selectedValue) ||
                  item.label ===
                    (selectedValue?.displayText || selectedValue?.label)
              ) || selectedValue
          : itemsArray?.find(
              (item: any) =>
                item?.value === selectedValue?.value &&
                item?.label === selectedValue?.label
            ) ||
            itemsArray?.find((item: any) => item?.value === selectedValue) ||
            selectedValue
      }
      onChange={(e: any) => {
        if (preferLang === "de" && e.originalItem) {
          onChange(e.originalItem);
        } else {
          onChange(e);
        }
      }}
      maxMenuHeight={3 * 40}
      isDisabled={isDisabled}
      isMulti={isMulti}
      onFocus={onFocus}
      noOptionsMessage={() =>
        !watch(`${subsectionName}.${subSectionIndex}.${validateWithFieldName}`)
          ? noOptionsMessage
          : undefined
      }
    />
  );
}
