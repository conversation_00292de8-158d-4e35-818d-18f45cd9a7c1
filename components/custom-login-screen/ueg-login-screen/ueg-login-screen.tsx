"use client";
import React, { useState, useEffect } from "react";
import { Eye, EyeOff, Mail, Lock, ChevronRight } from "lucide-react";
import Image from "next/image";
import {
  Carousel,
  type CarouselApi,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";

import { useRouter } from "next/navigation";
import { useFormContext } from "react-hook-form";
import DynamicFields from "@/components/custom/DynamicFields";
import { sortOrder } from "@/helpers/Sorting";
import loader from "../../../public/loader.svg";
import { useRecaptcha } from "@/hooks/useRecaptcha";
import { verifyRecaptcha } from "@/api/api";
import { useAtom } from "jotai";
import { preferredLanguage } from "@/lib/atom";
import LanguageSelector from "@/components/custom/translate";
import enTranslations from "../../../translations/en.json";
import deTranslations from "../../../translations/de.json";
import { campusData } from "./constant";
import { socialMediaLinks } from "./constant";

interface LoginScreenProps {
  handleLogin: () => void;
  formQuery: any;
  errorMessage?: string;
  setErrorMessage: (message: string) => void;
  isRecaptchaEnabled: boolean;
  pageQuery: any;
  apiKey: string;
}

export default function UegLoginScreen({
  handleLogin,
  formQuery,
  errorMessage,
  setErrorMessage,
  isRecaptchaEnabled,
  pageQuery,
  apiKey,
}: LoginScreenProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isActiveCurosel, setIsActiveCurosel] = useState(0);
  const [api, setApi] = useState<CarouselApi>();
  const [canScrollPrev, setCanScrollPrev] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(false);
  const [campusApi, setCampusApi] = useState<CarouselApi>();
  const [campusCanScrollPrev, setCampusCanScrollPrev] = useState(false);
  const [campusCanScrollNext, setCampusCanScrollNext] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useAtom(preferredLanguage);

  const {
    register,
    setValue,
    watch,
    clearErrors,
    formState: { errors },
    trigger,
    setError,
    getValues,
  } = useFormContext();

  const { executeRecaptcha, isRecaptchaLoaded, recaptchaError } =
    useRecaptcha();

  useEffect(() => {
    if (errorMessage) {
      setIsLoading(false);
    }
  }, [errorMessage]);

  useEffect(() => {
    if (!api) {
      return;
    }
    setIsActiveCurosel(api.selectedScrollSnap());
    setCanScrollPrev(api.canScrollPrev());
    setCanScrollNext(api.canScrollNext());

    api.on("select", () => {
      setIsActiveCurosel(api.selectedScrollSnap());
      setCanScrollPrev(api.canScrollPrev());
      setCanScrollNext(api.canScrollNext());
    });
  }, [api]);

  useEffect(() => {
    if (!campusApi) {
      return;
    }

    setCampusCanScrollPrev(campusApi.canScrollPrev());
    setCampusCanScrollNext(campusApi.canScrollNext());

    campusApi.on("select", () => {
      setCampusCanScrollPrev(campusApi.canScrollPrev());
      setCampusCanScrollNext(campusApi.canScrollNext());
    });
  }, [campusApi]);

  // Translation function using JSON files
  const t = (key: string): string => {
    const translations =
      selectedLanguage === "de" ? deTranslations : enTranslations;
    const keys = key.split(".");
    let value: any = translations;

    for (const k of keys) {
      if (value && typeof value === "object" && k in value) {
        value = value[k];
      } else {
        return key; // Return key if translation not found
      }
    }

    return typeof value === "string" ? value : key;
  };

  // Helper to get footer links directly from translation file
  const getFooterLinks = () => {
    const translations =
      selectedLanguage === "de" ? deTranslations : enTranslations;
    return translations.uegLogin?.footerLinks || [];
  };

  const footerLinks = getFooterLinks();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const fieldNames = formQuery?.fieldData
      ?.filter((item: any) => item?.indexOrder <= 2)
      ?.map((item: any) => item?.fieldName);

    const loginDetails = getValues();

    Object.keys(loginDetails).forEach((key) => {
      const value = loginDetails[key];
      if (typeof value === "string" && value.trim() === "") {
        setValue(key, "");
        loginDetails[key] = "";
      }
    });

    const isValid = await trigger(fieldNames);

    if (!isValid) {
      return;
    }
    setIsLoading(true);

    // Check for reCAPTCHA errors first
    if (isRecaptchaEnabled && recaptchaError) {
      setIsLoading(false);
      console.error("reCAPTCHA error:", recaptchaError);
      return;
    }

    try {
      if (isRecaptchaEnabled) {
        console.log(isRecaptchaEnabled);
        // Execute reCAPTCHA verification
        const recaptchaToken = await executeRecaptcha("login");
        if (!recaptchaToken) {
          setIsLoading(false);
          console.error("reCAPTCHA verification failed");
          return;
        }

        // Verify reCAPTCHA token
        const recaptchaResult = await verifyRecaptcha(recaptchaToken, "login", apiKey);
        if (!recaptchaResult.success) {
          setIsLoading(false);
          setErrorMessage(
            recaptchaResult?.error || "reCAPTCHA verification failed"
          );
          console.error(
            "reCAPTCHA verification failed:",
            recaptchaResult.error
          );
          return;
        }
      }
      // Proceed with login if reCAPTCHA is successful
      await handleLogin();
    } catch (error) {
      setIsLoading(false);
    }
  };

  return (
    <div className="h-screen w-full overflow-y-scroll">
      <div className="min-h-screen flex flex-col min-w-full max-sm:min-w-0 max-sm:block">
        {/* Header */}
        <header className="py-4 px-4 sm:px-6 md:px-8 lg:px-16 w-full pt-8 max-sm:px-2 max-sm:py-2 max-sm:flex max-sm:flex-row max-sm:items-center max-sm:gap-2">
          <div className="flex items-center justify-between w-full max-sm:flex-row max-sm:items-center max-sm:w-full max-sm:gap-2">
            {/* Logo */}
            <Image
              src="/ueglogin/ueg-logo-1.png"
              alt="University of Europe"
              width={200}
              height={60}
              className="h-10 sm:h-12 w-auto object-contain"
            />

            {/* Language Selector */}
            <div
              className="min-w-[130px] max-sm:min-w-0 max-sm:w-auto max-sm:flex-1 max-sm:justify-end max-sm:flex"
              role="button"
              aria-label="Select language"
            >
              <LanguageSelector
                languageData={{
                  ...pageQuery?.languageData,
                  styles: {
                    border: "1px solid #ccc",
                    padding: "2px 8px",
                    borderRadius: "4px",
                    color: "#374151",
                    fontSize: "16px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    gap: "8px",
                    flexDirection: "row",
                    flexWrap: "wrap",
                  },
                }}
              />
            </div>
          </div>
        </header>

        {/* Main content */}
        <div className="flex flex-col lg:flex-row px-4 sm:px-6 md:px-8 lg:px-14 pt-2 sm:pt-8 lg:pt-12 pb-6 w-full sm:w-[clamp(200px, 50%, 600px)]">
          <div className="border border-black rounded-2xl overflow-hidden flex flex-col lg:flex-row w-full">
            {/* Left Panel - University Branding */}
            <div className="w-full  lg:w-[42%] bg-[url('/ueglogin/login.png')] rounded-2xl bg-cover bg-center text-white pt-3">
              <div className="h-full flex flex-col justify-center px-2 sm:px-3 md:px-4 lg:px-5 py-4 sm:py-6 md:py-7 lg:py-8">
                <div className="max-w-md mx-auto w-full">
                  <h1 className="text-xs sm:text-sm md:text-sm pb-3 sm:pb-5">
                    {t("uegLogin.branding.subtitle")}
                  </h1>

                  <h2
                    className="text-2xl sm:text-3xl md:text-4xl font-bold mb-3 sm:mb-4"
                    dangerouslySetInnerHTML={{
                      __html: t("uegLogin.branding.title"),
                    }}
                  />

                  <div className="space-y-3 mb-6 sm:mb-8">
                    <div className="bg-white/50 backdrop-blur-sm rounded-lg p-3 sm:p-4 md:p-6">
                      <div className="flex items-center p-2 sm:p-3 border-b border-white/30">
                        <p className="text-base sm:text-lg md:text-xl font-bold text-white w-16 sm:w-20">
                          7500+
                        </p>
                        <p className="text-xs sm:text-sm text-white flex-1 ml-4 sm:ml-6">
                          {t("uegLogin.branding.stats.enrolledStudents")}
                        </p>
                      </div>
                      <div className="flex items-center p-2 sm:p-3 border-b border-white/30">
                        <div className="text-base sm:text-lg md:text-xl font-bold text-white w-16 sm:w-20">
                          25+
                        </div>
                        <div className="text-xs sm:text-sm text-white flex-1 ml-4 sm:ml-6">
                          {t("uegLogin.branding.stats.yearsTeaching")}
                        </div>
                      </div>
                      <div className="flex items-center p-2 sm:p-3 border-b border-white/30">
                        <div className="text-base sm:text-lg md:text-xl font-bold text-white w-16 sm:w-20">
                          140+
                        </div>
                        <div className="text-xs sm:text-sm text-white flex-1 ml-4 sm:ml-6">
                          {t("uegLogin.branding.stats.nationalities")}
                        </div>
                      </div>
                      <div className="flex items-center p-2 sm:p-3">
                        <div className="text-base sm:text-lg md:text-xl font-bold text-white w-16 sm:w-20">
                          &lt;90%
                        </div>
                        <div className="text-xs sm:text-sm text-white flex-1 ml-4 sm:ml-6">
                          {t("uegLogin.branding.stats.graduateJobs")}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white rounded-lg p-2 sm:p-3 md:p-6">
                    <div className="flex items-center justify-center">
                      <Image
                        key={selectedLanguage}
                        src={
                          selectedLanguage === "de"
                            ? "/ueglogin/uegad-de.png"
                            : "/ueglogin/uegad.png"
                        }
                        alt="award"
                        width={380}
                        height={150}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Panel - Login Form */}
            <div className="w-full lg:w-[57%] flex items-center justify-center bg-white px-2 sm:px-3 md:px-4 lg:pl-5 xl:pl-7 2xl:pl-10 py-4 lg:py-0">
              <div className="w-full max-w-md lg:w-[54%] lg:max-w-md">
                <div className="text-center mb-4 sm:mb-6">
                  <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-2">
                    {t("uegLogin.welcome")}
                  </h2>
                </div>

                <form onSubmit={handleSubmit} className="flex flex-col gap-4">
                  {sortOrder(formQuery?.fieldData, "indexOrder")
                    ?.filter((item: any) => item?.indexOrder <= 2)
                    ?.map((item: any, index: any) => (
                      <div key={index} className="flex flex-col">
                        <DynamicFields
                          register={register}
                          selectedValue={
                            watch(item?.fieldName) ||
                            watch(`${item?.documentType}`) ||
                            ""
                          }
                          disabled={
                            item?.disabledWhen
                              ? watch(item?.disabledWhen?.fieldName)?.label ===
                                item?.disabledWhen?.value
                              : false
                          }
                          isVisibleWhen
                          fieldItem={item}
                          label={
                            item?.label ||
                            item?.displayName ||
                            item?.placeholder
                          }
                          handleValueChanged={(value: any, type?: string) => {
                            if (item?.childField && item?.setValue) {
                              if (value?.value == item?.value) {
                                setValue(item?.childField, item?.setValue);
                                clearErrors(item?.childField);
                              } else {
                                setValue(item?.childField, "");
                              }
                            }
                            clearErrors(item?.fieldName);
                            clearErrors(`${item?.documentType}`);
                            if (type === "pickList" && item?.fieldDisplayName) {
                              setValue(item?.fieldDisplayName, value);
                            }
                            if (item?.resetChild) {
                              setValue(item?.resetChild, "");
                              clearErrors(item?.resetChild);
                            }
                            setValue(item?.fieldName, value);
                          }}
                          errorMessage={
                            errors?.[item?.fieldName]?.message ||
                            errors?.[`${item?.documentType}`]?.message
                          }
                          name={item?.fieldName}
                          trigger={trigger}
                          watch={watch}
                          clearErrors={clearErrors}
                          setError={setError}
                          displayNoTitle={false}
                          setValue={setValue}
                        />
                      </div>
                    ))}

                  {errorMessage && (
                    <div className="text-red-600 text-sm mt-2">
                      {errorMessage}
                    </div>
                  )}

                  <button
                    type="submit"
                    disabled={isLoading}
                    className="w-full bg-red-600 text-white py-3 px-4 hover:bg-red-700 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed mt-2"
                  >
                    {isLoading ? (
                      <div className="flex items-center justify-center">
                        <Image
                          priority
                          src={loader}
                          height={32}
                          width={32}
                          alt="Loading..."
                        />
                      </div>
                    ) : (
                      t("uegLogin.login")
                    )}
                  </button>

                  <div className="flex justify-center items-center py-3">
                    <span className="text-gray-500 text-sm">
                      {t("uegLogin.or")}
                    </span>
                  </div>

                  <button
                    type="button"
                    onClick={() => router.push("/application")}
                    className="w-full bg-[#EAE4DB] text-black py-3 px-4 focus:outline-none"
                  >
                    {t("uegLogin.newUser")}
                  </button>

                  <div className="border-t border-gray-200 my-4"></div>

                  <div className="text-center">
                    <span
                      onClick={() => router.push("/forgot-password")}
                      className="text-sm text-[#797A7A] cursor-pointer"
                    >
                      {t("uegLogin.forgotPassword")}
                    </span>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>

        {/* Contact section */}
        <div className="px-4 sm:px-6 md:px-8 lg:px-16 pt-6 sm:pt-8 lg:pt-10">
          <div className="flex flex-col md:flex-row items-center justify-between bg-[url('/ueglogin/emergency.png')] bg-cover bg-center text-white p-4 sm:p-6 md:p-8 lg:p-12 rounded-xl">
            <div className="text-center md:text-left mb-4 md:mb-0">
              <h3 className="text-lg sm:text-xl md:text-2xl font-bold mb-2 sm:mb-3">
                {t("uegLogin.callbackRequest.title")}
              </h3>
              <p className="text-sm md:text-sm leading-relaxed font-normal">
                {t("uegLogin.callbackRequest.description")}
              </p>
              <div className="mt-3 sm:mt-4">
                <a
                  href="https://www.ue-germany.com/callback"
                  target="_blank"
                  className="inline-block bg-red-600 text-white px-4 sm:px-6 py-2 text-sm hover:bg-red-700 transition-colors font-bold"
                >
                  {t("uegLogin.callbackRequest.button")}
                </a>
              </div>
            </div>
          </div>
        </div>

        <div className="w-full sm:w-11/12 md:w-9/12 mx-auto border-b-2 border-red-600 py-3 sm:py-4 md:py-8"></div>

        {/* Campus section */}
        <div className="pt-3 sm:pt-4 md:pt-8 px-4 sm:px-0">
          <div className="container mx-auto">
            <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-center pb-2">
              {t("uegLogin.campus.title")}
            </h2>
            <p className="text-sm text-center mb-6 sm:mb-8 lg:mb-10 px-4">
              {t("uegLogin.campus.subtitle")}
            </p>
            <Carousel
              className="w-full"
              setApi={setCampusApi}
              opts={{
                align: "start",
                loop: false,
                slidesToScroll: 1,
                breakpoints: {
                  // You can adjust slidesToScroll for larger screens if needed
                  "(min-width: 650px)": {
                    slidesToScroll: 1,
                  },
                  "(min-width: 768px)": {
                    slidesToScroll: 1,
                  },
                  "(min-width: 1024px)": {
                    slidesToScroll: 1,
                  },
                },
              }}
            >
              <CarouselContent className="-ml-1 sm:-ml-2 md:-ml-3 lg:-ml-4">
                {campusData.map((campus) => (
                  <CarouselItem
                    key={campus.id}
                    className="pl-1 sm:pl-2 md:pl-3 lg:pl-4 basis-full sm:basis-1/2 md:basis-1/3 lg:basis-1/4 flex"
                  >
                    <div className="rounded-2xl shadow-md overflow-hidden w-full h-[350px] sm:h-[400px] md:h-[480px] relative">
                      <div
                        className="absolute inset-0 bg-cover bg-center"
                        style={{
                          backgroundImage: `url(${campus.image.src})`,
                        }}
                      ></div>
                      <div className="p-4 sm:p-6 h-full flex flex-col justify-end bg-black/40 relative z-10 min-h-[180px] sm:min-h-[200px] md:min-h-[220px] lg:min-h-[240px] 2xl:min-h-[260px]">
                        <h3 className="font-bold text-white text-base sm:text-lg mb-2 max-sm:text-xs">
                          {t(`uegLogin.campus.locations.${campus.key}.title`)}
                        </h3>
                        <p className="text-xs sm:text-[13px] font-normal text-[#FFFFFF] mb-4 sm:mb-6 md:min-h-[76px] max-sm:text-[10px]">
                          {t(
                            `uegLogin.campus.locations.${campus.key}.description`
                          )}
                        </p>
                        <a
                          href={
                            (selectedLanguage === "de"
                              ? (deTranslations.uegLogin.campus
                                  .locations as Record<string, any>)
                              : (enTranslations.uegLogin.campus
                                  .locations as Record<string, any>))[
                              campus.key
                            ]?.link
                          }
                          target="_blank"
                          className="text-sm text-white underline underline-offset-4 font-normal flex flex-row items-center max-sm:text-xs max-sm:px-2 max-sm:py-1 max-sm:h-7 max-sm:min-w-[60px] max-sm:rounded"
                        >
                          {t("uegLogin.campus.getMoreInfo")}
                          <span className="text-center ml-0.2">
                            {" "}
                            <ChevronRight className="h-4 w-4 max-sm:w-3 max-sm:h-3" />
                          </span>
                        </a>
                      </div>
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>
              <button
                onClick={() => campusApi?.scrollPrev()}
                className={`absolute left-4 sm:left-8 md:left-12 lg:left-16 top-1/2 -translate-y-1/2 z-20 bg-transparent border-none cursor-pointer transition-opacity duration-200 max-sm:w-7 max-sm:h-7 max-sm:p-1 ${
                  !campusCanScrollPrev
                    ? "opacity-0 pointer-events-none"
                    : "opacity-100"
                }`}
                aria-label="Previous slide"
              >
                <svg
                  width="59"
                  height="59"
                  viewBox="0 0 59 59"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="max-sm:w-7 max-sm:h-7"
                  style={{ transform: "scaleX(-1)" }}
                >
                  <path
                    d="M30.975 29.5L19.6666 18.1917L23.1083 14.75L37.8583 29.5L23.1083 44.25L19.6666 40.8083L30.975 29.5Z"
                    fill="#FEF7FF"
                  />
                </svg>
              </button>

              <button
                onClick={() => campusApi?.scrollNext()}
                className={`absolute right-4 sm:right-8 md:right-12 lg:right-16 top-1/2 -translate-y-1/2 z-20 bg-transparent border-none cursor-pointer transition-opacity duration-200 max-sm:w-7 max-sm:h-7 max-sm:p-1 ${
                  !campusCanScrollNext
                    ? "opacity-0 pointer-events-none"
                    : "opacity-100"
                }`}
                aria-label="Next slide"
              >
                <svg
                  width="59"
                  height="59"
                  viewBox="0 0 59 59"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="max-sm:w-7 max-sm:h-7"
                >
                  <path
                    d="M30.975 29.5L19.6666 18.1917L23.1083 14.75L37.8583 29.5L23.1083 44.25L19.6666 40.8083L30.975 29.5Z"
                    fill="#FEF7FF"
                  />
                </svg>
              </button>
            </Carousel>
          </div>
        </div>

        <div className="w-full sm:w-11/12 md:w-9/12 mx-auto border-b-2 border-red-600 py-3 sm:py-4 md:py-8"></div>

        {/* Testimonials section */}
        <div className="pt-4 sm:pt-6 md:pt-8 lg:pt-10 relative">
          <div className="container mx-auto px-4 sm:px-6 md:px-8">
            <div className="flex flex-col justify-center items-center max-w-6xl mx-auto">
              <h1 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-center mb-2 sm:mb-4 md:mb-4 max-w-4xl">
                {t("uegLogin.testimonials.title")}
              </h1>
              <p className="text-sm sm:text-base md:text-sm text-center mb-8 sm:mb-10 md:mb-12 max-w-4xl text-gray-700 leading-relaxed font-normal">
                {t("uegLogin.testimonials.subtitle")}
              </p>
            </div>
          </div>

          <div className="w-full">
            <Carousel
              className="w-full"
              setApi={setApi}
              opts={{
                align: "start",
                loop: true,
              }}
            >
              <CarouselContent>
                <CarouselItem className="md:basis-full">
                  <div className="relative w-full h-[350px] sm:h-[450px] md:h-[550px] lg:h-[650px] 2xl:h-[1050px] overflow-hidden">
                    <Image
                      src="/ueglogin/user-1.png"
                      alt="Student testimonial"
                      fill
                      className="object-cover object-center w-full h-full"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 90vw, 80vw"
                      priority
                      style={{
                        objectFit: "cover",
                        width: "100%",
                        height: "100%",
                      }}
                    />
                    <div className="absolute right-[8%] sm:right-[10%] md:right-[12%] lg:right-[15%] top-1/2 -translate-y-1/2 bg-white p-3 sm:p-4 md:p-6 rounded-2xl sm:rounded-3xl shadow-lg w-[240px] sm:w-[320px] md:w-[380px] max-w-[calc(100vw-2rem)] max-sm:w-[200px] max-sm:p-2 max-sm:rounded-xl max-sm:mx-2">
                      <h3 className="font-bold text-sm max-sm:text-xs sm:text-lg mb-2">
                        {t("uegLogin.testimonials.students.sarah.name")}
                      </h3>
                      <p className="text-[11px] max-sm:text-[10px] sm:text-sm text-gray-600 italic mb-3">
                        {t("uegLogin.testimonials.students.sarah.text")}
                      </p>
                      <div className="flex items-center">
                        <a
                          href={
                            selectedLanguage === "de"
                              ? deTranslations.uegLogin.testimonials.students
                                  .sarah.link
                              : enTranslations.uegLogin.testimonials.students
                                  .sarah.link
                          }
                          target="_blank"
                          className="text-xs sm:text-sm flex items-center gap-0.5 text-gray-600 max-sm:text-[10px] max-sm:px-2 max-sm:py-1 max-sm:h-7 max-sm:min-w-[60px] max-sm:rounded"
                        >
                          {t("uegLogin.testimonials.readMore")}
                          <ChevronRight className="w-4 h-4 max-sm:w-3 max-sm:h-3" />
                        </a>
                      </div>
                    </div>
                  </div>
                </CarouselItem>

                <CarouselItem className="md:basis-full">
                  <div className="relative w-full h-[350px] sm:h-[450px] md:h-[550px] lg:h-[650px] 2xl:h-[1050px] overflow-hidden">
                    <Image
                      src="/ueglogin/user-2.png"
                      alt="Student testimonial"
                      fill
                      className="object-cover object-center w-full h-full"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 90vw, 80vw"
                      priority
                      style={{
                        objectFit: "cover",
                        width: "100%",
                        height: "100%",
                      }}
                    />
                    <div className="absolute left-[8%] sm:left-[10%] md:left-[12%] lg:left-[15%] top-1/2 -translate-y-1/2 bg-white p-3 sm:p-4 md:p-6 rounded-2xl sm:rounded-3xl shadow-lg w-[240px] sm:w-[320px] md:w-[380px] max-w-[calc(100vw-2rem)] max-sm:w-[200px] max-sm:p-2 max-sm:rounded-xl max-sm:mx-2">
                      <h3 className="font-bold text-sm max-sm:text-xs sm:text-lg">
                        {t("uegLogin.testimonials.students.jorge.name")}
                      </h3>
                      <p className="text-[11px] max-sm:text-[10px] sm:text-sm text-gray-600 italic my-2">
                        {t("uegLogin.testimonials.students.jorge.text")}
                      </p>
                      <div className="flex items-center mt-2">
                        <a
                          href={
                            selectedLanguage === "de"
                              ? deTranslations.uegLogin.testimonials.students
                                  .jorge.link
                              : enTranslations.uegLogin.testimonials.students
                                  .jorge.link
                          }
                          target="_blank"
                          className="text-xs sm:text-sm flex items-center gap-0.5 text-gray-600 max-sm:text-[10px] max-sm:px-2 max-sm:py-1 max-sm:h-7 max-sm:min-w-[60px] max-sm:rounded"
                        >
                          {t("uegLogin.testimonials.readMore")}
                          <ChevronRight className="w-4 h-4 max-sm:w-3 max-sm:h-3" />
                        </a>
                      </div>
                    </div>
                  </div>
                </CarouselItem>
              </CarouselContent>

              <button
                onClick={() => api?.scrollPrev()}
                className={`absolute left-4 sm:left-8 md:left-12 lg:left-16 top-1/2 -translate-y-1/2 z-20 bg-transparent border-none cursor-pointer transition-opacity duration-200 max-sm:w-7 max-sm:h-7 max-sm:p-0.5 md:p-0.5 ${
                  !canScrollPrev
                    ? "opacity-0 pointer-events-none"
                    : "opacity-100"
                }`}
                aria-label="Previous slide"
              >
                <svg
                  width="59"
                  height="59"
                  viewBox="0 0 59 59"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="max-sm:w-7 max-sm:h-7"
                  style={{ transform: "scaleX(-1)" }}
                >
                  <path
                    d="M30.975 29.5L19.6666 18.1917L23.1083 14.75L37.8583 29.5L23.1083 44.25L19.6666 40.8083L30.975 29.5Z"
                    fill="#FEF7FF"
                  />
                </svg>
              </button>

              <button
                onClick={() => api?.scrollNext()}
                className={`absolute right-4 sm:right-8 md:right-12 lg:right-16 top-1/2 -translate-y-1/2 z-20 bg-transparent border-none cursor-pointer transition-opacity duration-200 max-sm:w-7 max-sm:h-7 max-sm:p-0.5 md:p-0.5 ${
                  !canScrollNext
                    ? "opacity-0 pointer-events-none"
                    : "opacity-100"
                }`}
                aria-label="Next slide"
              >
                <svg
                  width="59"
                  height="59"
                  viewBox="0 0 59 59"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="max-sm:w-7 max-sm:h-7"
                >
                  <path
                    d="M30.975 29.5L19.6666 18.1917L23.1083 14.75L37.8583 29.5L23.1083 44.25L19.6666 40.8083L30.975 29.5Z"
                    fill="#FEF7FF"
                  />
                </svg>
              </button>

              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2 sm:space-x-3 z-20 justify-center items-center">
                {Array.from({ length: 2 }).map((_, index) => (
                  <span
                    key={index}
                    className={`h-2 w-2 sm:h-3 sm:w-3 rounded-full transition-colors duration-200 ${
                      isActiveCurosel === index ? "bg-red-600" : "bg-gray-300"
                    } `}
                  ></span>
                ))}
              </div>
            </Carousel>
          </div>

          {/* Footer */}
          <footer
            className="bg-[#20242A] text-white pt-10 sm:pt-16 md:pt-20 pb-8 sm:pb-12 md:pb-16"
            style={{ marginTop: 0 }}
          >
            <div className="container mx-auto px-4 sm:px-8 md:px-12 lg:px-20">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-12 gap-6 sm:gap-8">
                {/* Left Column - Logo and Accreditation */}
                <div className="lg:col-span-4">
                  <div className="mb-6 sm:mb-8">
                    <Image
                      src="/ueglogin/ueg-logo-2.png"
                      alt="University of Europe for Applied Sciences"
                      width={200}
                      height={60}
                      className="mb-6 sm:mb-8 max-w-full h-auto max-sm:max-w-[200px] max-sm:h-auto"
                    />
                  </div>

                  <div className="mb-6 sm:mb-8">
                    <p className="text-sm text-gray-300 mb-4">
                      {t("uegLogin.footer.accreditedBy")}
                    </p>
                    <div className="flex flex-wrap gap-4">
                      <Image
                        src="/ueglogin/accrecated.png"
                        alt="Accreditation logos"
                        width={400}
                        height={120}
                        className="mb-4 max-w-full h-auto max-sm:max-w-[200px] max-sm:h-auto"
                      />
                    </div>
                  </div>

                  <div className="mb-6 sm:mb-8">
                    <p className="text-sm text-gray-300 mb-4">
                      {t("uegLogin.footer.memberOf")}
                    </p>
                    <Image
                      src="/ueglogin/member.png"
                      alt="Member organizations"
                      width={160}
                      height={80}
                      className="mb-4 max-w-full h-auto max-sm:max-w-[200px] max-sm:h-auto"
                    />
                  </div>
                </div>

                {/* Middle Column - Social Media */}
                <div className="lg:col-span-3 lg:col-start-6 flex flex-col items-center lg:items-start max-sm:items-start">
                  <h4 className="text-sm font-semibold mb-4 sm:mb-6 text-center lg:text-left w-full max-sm:text-left max-sm:w-auto">
                    {t("uegLogin.footer.followUs")}
                  </h4>
                  <div className="flex flex-row gap-4 justify-center lg:justify-start w-full max-sm:justify-start max-sm:w-auto">
                    {socialMediaLinks.map((social) => (
                      <a
                        key={social.id}
                        href={social.href}
                        className="text-gray-300 hover:text-red-300 transition-colors"
                        title={social.name}
                        target="_blank"
                      >
                        <svg
                          className="w-5 h-5 sm:w-6 sm:h-6"
                          fill="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path d={social.icon} />
                        </svg>
                      </a>
                    ))}
                  </div>
                </div>

                {/* Right Columns - Links */}
                <div className="lg:col-span-4 lg:col-start-9 text-left lg:text-right">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-8 sm:gap-16">
                    {footerLinks.map((column) => (
                      <div key={column.id}>
                        <ul className="space-y-2 sm:space-y-3 text-lg">
                          {column.links.map((link, index) => (
                            <li key={index}>
                              <a
                                href={link.href}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-gray-300 hover:text-red-300 transition-colors text-sm whitespace-nowrap"
                              >
                                {link.name}
                              </a>
                            </li>
                          ))}
                        </ul>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Bottom Section */}
              <div className="pt-6 sm:pt-8 mt-8 sm:mt-12">
                <div className="text-[10.5px] sm:text-sm">
                  <p
                    className="mb-4"
                    dangerouslySetInnerHTML={{
                      __html: t("uegLogin.footer.description"),
                    }}
                  />
                </div>
              </div>
            </div>
          </footer>
        </div>
      </div>
    </div>
  );
}
