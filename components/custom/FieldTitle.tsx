import { Label } from "../ui/label";
import { useAtom } from "jotai";
import { fontSizeAtom } from "@/lib/atom";
import { getBrandSpecificFontStyle } from "@/lib/brandUtils";
interface TitleProps {
  label: string | undefined;
  isMandatory?: boolean;
  htmlFor?: string;
  isPreview?: boolean;
}

export const FieldTitle = ({
  label,
  isMandatory,
  htmlFor,
  isPreview,
}: TitleProps) => {
  const [fontSize] = useAtom(fontSizeAtom);
  if (isPreview) {
    return (
      <div className="mb-1">
        <Label htmlFor={htmlFor} className="text-xl font-bold"> 
          {label || ""}
        </Label>
      </div>
    );
  } else
    return (
      <div className="mb-1">
        <Label
          htmlFor={htmlFor}
          className="text-sm"
          style={getBrandSpecificFontStyle(fontSize, "label")}
        >
          {label || ""}
        </Label>
        {isMandatory ? (
          <Label className="text-sm ml-1 text-red-500">*</Label>
        ) : (
          <></>
        )}
      </div>
    );
};
