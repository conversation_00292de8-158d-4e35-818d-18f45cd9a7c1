import { useCallback, useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { staticContentsAtom } from "@/lib/atom";
import { useAtom } from "jotai";

interface YearPickerProps {
  handleValueChanged: Function;
  value: string;
  label?: string;
  register?: any;
  setError?: any;
  watch?: any;
  clearErrors?: any;
  fieldName?: any;
  validateOn?: any;
  validateWith?: any;
  onBlur?: any;
  disabled?: boolean;
}

export default function YearPicker({
  handleValueChanged,
  value,
  register,
  clearErrors,
  watch,
  setError,
  validateOn,
  validateWith,
  label,
  fieldName,
  onBlur,
  disabled,
}: YearPickerProps) {
  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 100 }, (_, i) => currentYear - i);
  const [staticContent] = useAtom<any>(staticContentsAtom);

  const validateFutureDate = (
    fieldName: any,
    selectedDate: any,
    currentDate: any,
    label: any
  ) => {
    if (selectedDate && new Date(selectedDate) > currentDate) {
      setError(fieldName, {
        type: "manual",
        message:
          staticContent?.errors?.dateValidation?.futureDateNotAllowed?.replace(
            "${label}",
            label
          ) || `${label} cannot be in the future`,
      });
    } else {
      clearErrors(fieldName);
    }
  };

  function validatePastDate(
    fieldName: any,
    selectedDate: any,
    currentDate: any,
    label: any
  ) {
    if (selectedDate && new Date(selectedDate) < currentDate) {
      setError(fieldName, {
        type: "manual",
        message:
          staticContent?.errors?.dateValidation?.pastDateNotAllowed?.replace(
            "${label}",
            label
          ) || `${label} cannot be in the past`,
      });
    } else {
      clearErrors(fieldName);
    }
  }
  const isAtLeast10YearsOld = (date: any) => {
    if (!date) return true;
    const tenYearsAgo = new Date();
    tenYearsAgo.setFullYear(tenYearsAgo.getFullYear() - 10);
    return new Date(date) <= tenYearsAgo;
  };

  const handleDateValidation = (
    fieldName: any,
    validateOn: any,
    validateWith: any
  ) => {
    const selectedDate = watch(fieldName);

    const validateDateWith = watch(
      fieldName.replace(/^((?:[^.]*\.){2})[^.]+/, `$1${validateWith}`)
    );

    var validateDate = new Date(validateDateWith);

    const currentDate = new Date();
    if (validateDateWith && validateWith) {
      if (new Date(selectedDate) < validateDate) {
        setError(fieldName, {
          type: "custom",
          message:
            staticContent?.errors?.dateValidation?.endDateBeforeStartDate?.replace(
              "${label}",
              label
            ) || `${label} Date cannot be less than the Start Date`,
        });
      } else if (validateOn === "futureDate") {
        validateFutureDate(fieldName, selectedDate, currentDate, label);
      } else if (validateOn === "pastDate") {
        validatePastDate(fieldName, selectedDate, currentDate, label);
      } else if (validateOn === "minimum_10_years") {
        if (!isAtLeast10YearsOld(selectedDate)) {
          setError(fieldName, {
            type: "custom",
            message:
              staticContent?.errors?.dateValidation?.minimumAge10?.replace(
                "${label}",
                label
              ) || `${label} Date must be at least 10 years old`,
          });
        } else {
          // console.log("current date", currentDate);
        }
      }
    } else if (validateOn === "futureDate") {
      validateFutureDate(fieldName, selectedDate, currentDate, label);
    } else if (validateOn === "pastDate") {
      validatePastDate(fieldName, selectedDate, currentDate, label);
    } else if (validateOn === "minimum_10_years") {
      if (!isAtLeast10YearsOld(selectedDate)) {
        setError(fieldName, {
          type: "custom",
          message: `${label} must be atleast 10 years old`,
        });
      } else {
        console.log("current date", currentDate);
      }
    }
  };
  useEffect(() => {
    handleDateValidation(fieldName, validateOn, validateWith);
  }, [fieldName, validateOn, validateWith]);

  return (
    <div {...register}>
      <Select
        value={value}
        onValueChange={(year) => {
          handleValueChanged(year);
          handleDateValidation(fieldName, validateOn, validateWith);
        }}
        onOpenChange={onBlur}
      >
        <SelectTrigger className={`w-full  "border-border"`}>
          <SelectValue placeholder="Select year" />
        </SelectTrigger>
        <SelectContent>
          {years.map((year) => (
            <SelectItem key={year} value={year.toString()}>
              {year}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
