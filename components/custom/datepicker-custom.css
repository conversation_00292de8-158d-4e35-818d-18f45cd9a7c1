/* Custom styling for DatePicker component */

/* Month picker popper positioning */
.month-picker-popper {
  z-index: 999 !important;
}

.month-picker-calendar {
  font-family: inherit !important;
  border-radius: 6px !important;
  border: 1px solid #e2e8f0 !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

/* Fix navigation arrows alignment */
.react-datepicker__navigation {
  position: absolute !important;
  top: 13px !important;
  height: 30px !important;
  width: 30px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
}

/* Left navigation arrow alignment */
.react-datepicker__navigation--previous {
  left: 12px !important;
}

/* Right navigation arrow alignment */
.react-datepicker__navigation--next {
  right: 12px !important;
}

/* Custom arrow styles */
.react-datepicker__navigation-icon::before {
  border-color: #666 !important;
  border-width: 2px 2px 0 0 !important;
  height: 9px !important;
  width: 9px !important;
  position: absolute !important;
  top: 6px !important;
  display: block !important;
  content: "" !important;
  transform: rotate(-45deg) !important;
}

.react-datepicker__navigation-icon--previous::before {
  right: -4px !important;
  transform: rotate(-135deg) !important;
}

.react-datepicker__navigation-icon--next::before {
  left: -4px !important;
  transform: rotate(45deg) !important;
}

/* Header (Month-Year) styling */
.react-datepicker__header {
  padding-top: 12px !important;
  padding-bottom: 8px !important;
  background-color: #f8fafc !important;
  border-bottom: 1px solid #e2e8f0 !important;
}

.react-datepicker__current-month {
  font-weight: 600 !important;
  font-size: 16px !important;
  margin-bottom: 8px !important;
  text-align: center !important;
  padding: 0 40px !important;
}

/* Month layout improvements */
.react-datepicker__month {
  margin: 0 !important;
  padding: 0.4rem !important;
}

.react-datepicker__month .react-datepicker__month-text {
  margin: 4px !important;
  padding: 6px 12px !important;
  border-radius: 4px !important;
  display: inline-block !important;
  width: calc(33.3% - 8px) !important;
  text-align: center !important;
  line-height: 1.7 !important;
}

/* Month hover and selected styles */
.react-datepicker__month-text:hover {
  background-color: #f0f0f0 !important;
}

.react-datepicker__month-text--selected {
  background-color: #216ba5 !important;
  color: white !important;
}

/* Input styling */
.react-datepicker__input-container input {
  width: 100% !important;
  padding: 0.4rem 0rem !important;
}