import React from "react";
import { useAtom } from "jotai";
import { staticContentsAtom } from "@/lib/atom";

interface OcrLoaderModalProps {
  isOpen: boolean;
}

const OcrLoaderModal: React.FC<OcrLoaderModalProps> = ({ isOpen }) => {
  const [staticContents] = useAtom<any>(staticContentsAtom);
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-white rounded-lg shadow-xl p-8 flex flex-col items-center justify-center max-w-md w-full">
        <div className="mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 100 100"
            width="100"
            height="100"
            className="text-primary"
          >
            {/* <!-- Outer rotating ring with gradient --> */}
            <defs>
              <linearGradient
                id="ringGradient"
                x1="0%"
                y1="0%"
                x2="100%"
                y2="100%"
              >
                <stop offset="0%" stopColor="currentColor" stopOpacity="0.8" />
                <stop
                  offset="100%"
                  stopColor="currentColor"
                  stopOpacity="0.2"
                />
              </linearGradient>
            </defs>

            {/* <!-- Rotating outer ring --> */}
            <circle
              cx="50"
              cy="50"
              r="45"
              fill="none"
              stroke="url(#ringGradient)"
              strokeWidth="3"
              strokeDasharray="10,4"
              opacity="0.8"
            >
              <animateTransform
                attributeName="transform"
                type="rotate"
                from="0 50 50"
                to="360 50 50"
                dur="12s"
                repeatCount="indefinite"
              />
            </circle>

            {/* <!-- Inner rotating ring (opposite direction) --> */}
            <circle
              cx="50"
              cy="50"
              r="38"
              fill="none"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeDasharray="3,6"
              opacity="0.4"
            >
              <animateTransform
                attributeName="transform"
                type="rotate"
                from="360 50 50"
                to="0 50 50"
                dur="8s"
                repeatCount="indefinite"
              />
            </circle>

            {/* <!-- Document with rounded corners and shadow --> */}
            <g>
              {/* <!-- Shadow effect --> */}
              <rect
                x="27"
                y="17"
                width="40"
                height="55"
                rx="4"
                fill="currentColor"
                opacity="0.2"
              />

              {/* <!-- Main document --> */}
              <rect
                x="25"
                y="15"
                width="40"
                height="55"
                rx="4"
                fill="white"
                stroke="currentColor"
                strokeWidth="2"
              >
                <animate
                  attributeName="opacity"
                  values="1;0.9;1"
                  dur="2s"
                  repeatCount="indefinite"
                />
              </rect>

              {/* <!-- Document header --> */}
              <rect
                x="25"
                y="15"
                width="40"
                height="8"
                rx="4"
                fill="currentColor"
                opacity="0.1"
              />

              {/* <!-- Document lines --> */}
              <line
                x1="32"
                y1="30"
                x2="58"
                y2="30"
                stroke="currentColor"
                strokeWidth="1.5"
                opacity="0.6"
              />
              <line
                x1="32"
                y1="38"
                x2="58"
                y2="38"
                stroke="currentColor"
                strokeWidth="1.5"
                opacity="0.6"
              />
              <line
                x1="32"
                y1="46"
                x2="58"
                y2="46"
                stroke="currentColor"
                strokeWidth="1.5"
                opacity="0.6"
              />
              <line
                x1="32"
                y1="54"
                x2="48"
                y2="54"
                stroke="currentColor"
                strokeWidth="1.5"
                opacity="0.6"
              />
            </g>
            {/* 
  <!-- Animated search group --> */}
            <g>
              {/* <!-- Complex movement animation --> */}
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0,0; 3,-3; 5,-1; 3,2; 0,3; -3,2; -5,-1; -3,-3; 0,0"
                dur="3s"
                repeatCount="indefinite"
              />

              {/* <!-- Magnifying glass with gradient --> */}
              <defs>
                <linearGradient
                  id="lensGradient"
                  x1="0%"
                  y1="0%"
                  x2="100%"
                  y2="100%"
                >
                  <stop offset="0%" stopColor="currentColor" stopOpacity="1" />
                  <stop
                    offset="100%"
                    stopColor="currentColor"
                    stopOpacity="0.7"
                  />
                </linearGradient>
              </defs>

              {/* <!-- Magnifying glass lens --> */}
              <circle
                cx="50"
                cy="60"
                r="18"
                fill="none"
                stroke="url(#lensGradient)"
                strokeWidth="3"
                strokeLinecap="round"
              >
                <animate
                  attributeName="r"
                  values="18;19;18"
                  dur="1.5s"
                  repeatCount="indefinite"
                />
              </circle>

              {/* <!-- Magnifying glass handle with rounded cap --> */}
              <line
                x1="63"
                y1="73"
                x2="75"
                y2="85"
                stroke="currentColor"
                strokeWidth="3.5"
                strokeLinecap="round"
              >
                <animate
                  attributeName="strokeWidth"
                  values="3.5;4.5;3.5"
                  dur="1.5s"
                  repeatCount="indefinite"
                />
              </line>

              {/* <!-- Lens reflection highlight --> */}
              <path
                d="M40,55 Q42,50 47,52"
                fill="none"
                stroke="currentColor"
                strokeWidth="1.5"
                opacity="0.7"
                strokeLinecap="round"
              />
            </g>
          </svg>
        </div>
        <h2 className="text-xl font-semibold text-gray-800 mb-2">
          {staticContents?.application?.ocr_process || "Processing Document"}
        </h2>
        <p className="text-gray-600 text-center mb-4">
          {staticContents?.application?.ocr_extract ||
            "We're extracting information from your document. This may take a moment."}
        </p>
        <div className="w-full bg-gray-200 h-1 rounded-full overflow-hidden">
          <div
            className="bg-primary h-full animate-pulse"
            style={{ width: "100%" }}
          ></div>
        </div>
      </div>
    </div>
  );
};

export default OcrLoaderModal;
