import { useEffect, useState } from "react";
//@ts-ignore
import DatePicker, { registerLocale } from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { Calendar } from "lucide-react";
import { de, enGB } from "date-fns/locale";
import { useAtom } from "jotai";
import { preferredLanguage, staticContentsAtom } from "@/lib/atom";
import "./datepicker-custom.css"; // Import custom styles

// Register the locales
registerLocale("de", de);
registerLocale("en-GB", enGB);

interface DatePickerProps {
  handleValueChanged: Function;
  value: string;
  label?: string;
  register?: any;
  setError?: any;
  watch?: any;
  clearErrors?: any;
  fieldName?: any;
  validateOn?: any;
  validateWith?: any;
  onBlur?: any;
}

export default function MonthPicker({
  handleValueChanged,
  value,
  register,
  clearErrors,
  watch,
  setError,
  validateOn,
  validateWith,
  label,
  fieldName,
  onBlur,
}: DatePickerProps) {
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [preferLang] = useAtom(preferredLanguage);
  const [staticContent] = useAtom<any>(staticContentsAtom);

  // Get the appropriate locale object based on the preferred language
  const getLocale = () => {
    // Fall back to the preferred language from user settings

    console.log("Pref", preferLang);

    if (preferLang === "de") {
      return de;
    }

    // Default to English
    return enGB;
  };

  // Safely parse the date from string format
  useEffect(() => {
    if (value && value.includes("-")) {
      const [month, year] = value.split("-");
      if (month && year && !isNaN(Number(month)) && !isNaN(Number(year))) {
        const dateObj = new Date(Number(year), Number(month) - 1, 1);
        if (!isNaN(dateObj.getTime())) {
          setStartDate(dateObj);
        }
      }
    }
  }, [value]);

  const formatMonthYear = (date: Date | null): string => {
    if (!date || isNaN(date.getTime())) {
      return "";
    }
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const year = date.getFullYear().toString();
    return `${month}-${year}`;
  };

  const handleDateValidation = (
    fieldName: any,
    validateOn: any,
    validateWith: any
  ) => {
    if (!fieldName || !watch) return;

    const selectedDate = watch(fieldName);
    if (!selectedDate) return;

    const validateDateWith = validateWith
      ? watch(fieldName.replace(/^((?:[^.]*\.){2})[^.]+/, `$1${validateWith}`))
      : null;

    try {
      // Safe parsing of dates
      let date: Date | null = null;
      let validateDate: Date | null = null;

      if (selectedDate && selectedDate.includes("-")) {
        const [month, year] = selectedDate.split("-");
        if (month && year) {
          date = new Date(Number(year), Number(month) - 1, 1);
        }
      }

      if (validateDateWith && validateDateWith.includes("-")) {
        const [vMonth, vYear] = validateDateWith.split("-");
        if (vMonth && vYear) {
          validateDate = new Date(Number(vYear), Number(vMonth) - 1, 1);
        }
      }

      if (!date || isNaN(date.getTime())) return;

      const currentDate = new Date();

      if (validateDateWith && validateDate && !isNaN(validateDate.getTime())) {
        if (date < validateDate) {
          setError(fieldName, {
            type: "custom",
            message:
              staticContent?.errors?.dateValidation?.endDateBeforeStartDate?.replace(
                "${label}",
                label
              ) || `${label} Date cannot be less than the Start Date`,
          });
          return;
        }
      }

      if (validateOn === "futureDate") {
        if (date > currentDate) {
          setError(fieldName, {
            type: "custom",
            message: `${label} Date cannot be in the future`,
          });
          return;
        }
      } else if (validateOn === "pastDate") {
        if (date < currentDate) {
          setError(fieldName, {
            type: "custom",
            message: `${label} Date cannot be in the past`,
          });
          return;
        }
      }

      // Clear errors if all validations pass
      clearErrors?.(fieldName);
    } catch (error) {
      console.error("Date validation error:", error);
    }
  };

  console.log("get", getLocale());

  useEffect(() => {
    if (fieldName && validateOn) {
      handleDateValidation(fieldName, validateOn, validateWith);
    }
  }, [value, fieldName, validateOn, validateWith]);

  return (
    <div {...register} className="relative">
      <DatePicker
        selected={startDate}
        onChange={(date: Date | null) => {
          if (date && !isNaN(date.getTime())) {
            handleValueChanged(formatMonthYear(date));
            setStartDate(date);
          }
        }}
        locale={getLocale()}
        dateFormat="MM/yyyy"
        showMonthYearPicker
        className="w-full h-full rounded-md text-sm ring-offset-background  px-0 placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
        onBlur={onBlur}
        maxDate={validateOn === "futureDate" ? new Date() : undefined}
        minDate={validateOn === "pastDate" ? new Date() : undefined}
        showPopperArrow={false}
        popperClassName="month-picker-popper"
        calendarClassName="month-picker-calendar"
      />
      <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none">
        <Calendar size={16} />
      </span>
    </div>
  );
}
