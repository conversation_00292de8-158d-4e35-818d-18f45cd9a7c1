import { Label } from "@radix-ui/react-label";
import Image from "next/image";
import { useAtom } from "jotai";
import { appHeroAtom, fontSizeAtom } from "@/lib/atom";
import { getBrandSpecificFontStyle } from "@/lib/brandUtils";

export default function Header({
  title,
  logo,
  image,
  sectionQueryIsFetching,
  logoInfo,
  handleBackButton,
}: any) {
  const [fontSize] = useAtom(fontSizeAtom);
  const [isAppHero] = useAtom(appHeroAtom);
  return (
    <>
      <div
        className={`items-center h-20 w-full bg-white shadow-lg flex-row  hidden lg:flex md:flex ${
          isAppHero && "pl-16"
        }`}
      >
        {!isAppHero ? (
          <button
            className="mx-6 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 rounded-md p-1 transition-colors hover:bg-gray-100"
            onClick={handleBackButton}
            aria-label="Go back"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20px"
              height="20px"
              viewBox="0 0 52 52"
              className="text-slate-700 hover:text-slate-600 transition-colors duration-200"
              fill="currentColor"
            >
              <path
                d="M48.6,23H15.4c-0.9,0-1.3-1.1-0.7-1.7l9.6-9.6c0.6-0.6,0.6-1.5,0-2.1l-2.2-2.2c-0.6-0.6-1.5-0.6-2.1,0
	L2.5,25c-0.6,0.6-0.6,1.5,0,2.1L20,44.6c0.6,0.6,1.5,0.6,2.1,0l2.1-2.1c0.6-0.6,0.6-1.5,0-2.1l-9.6-9.6C14,30.1,14.4,29,15.3,29
	h33.2c0.8,0,1.5-0.6,1.5-1.4v-3C50,23.8,49.4,23,48.6,23z"
              />
            </svg>
          </button>
        ) : null}

        <div className=" items-center w-full flex-row  hidden lg:flex md:flex md:px-4">
          <img src={logo} className="h-8 w-8" height={32} width={32} />
          <Label
            className="mx-3 font-medium text-base"
            style={getBrandSpecificFontStyle(fontSize, "header-title")}
          >
            {title}
          </Label>
        </div>
      </div>

      <div className="md:px-20 items-center  h-20 w-full bg-on-background shadow-lg flex flex-row px-5 sm:flex md:hidden lg:hidden xl:hidden">
        {sectionQueryIsFetching ? (
          <></>
        ) : (
          image && (
            <Image
              priority
              src={image}
              height={logoInfo?.height ?? 28}
              width={logoInfo?.width ?? 60}
              style={{ objectFit: "contain" }}
              alt="college_logo"
            />
          )
        )}
      </div>
    </>
  );
}
