import React, { useEffect, useState } from "react";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface AlreadySubmittedModalProps {
  isOpen: boolean;
  onClose: () => void;
  onViewConfirmation: () => void;
  details?: any;
}

const AlreadySubmittedModal: React.FC<AlreadySubmittedModalProps> = ({
  isOpen,
  onClose,
  onViewConfirmation,
  details,
}) => {
  const [countdown, setCountdown] = useState(2);

  useEffect(() => {
    if (!isOpen) return;

    // Start countdown when modal opens
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          // Redirect when countdown reaches 0
          clearInterval(timer);
          onViewConfirmation();
          onClose();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    // Reset countdown when modal opens
    setCountdown(3);

    // Cleanup timer on unmount or when modal closes
    return () => {
      clearInterval(timer);
      setCountdown(3);
    };
  }, [isOpen, onViewConfirmation, onClose]);

  if (!isOpen) return null;

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent className="max-w-md border-0 shadow-lg">
        <div className="bg-primary h-2 w-full absolute top-0 left-0 rounded-t-lg"></div>
        <AlertDialogHeader className="flex flex-col items-center pt-6">
          <AlertDialogTitle className="text-xl font-semibold text-center text-foreground mb-2">
            {details.title}
          </AlertDialogTitle>
          <div className="text-center text-muted-foreground mb-6">
            {details.message}
          </div>
        </AlertDialogHeader>

        {/* Auto-redirect countdown section with main visual */}
        <div className="px-6 pb-6">
          <div className="mb-6 flex items-center justify-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 120 120"
              width="100"
              height="100"
              className="text-primary"
            >
              <defs>
                <linearGradient
                  id="redirectGradient"
                  x1="0%"
                  y1="0%"
                  x2="100%"
                  y2="0%"
                >
                  <stop
                    offset="0%"
                    stopColor="currentColor"
                    stopOpacity="0.2"
                  />
                  <stop
                    offset="50%"
                    stopColor="currentColor"
                    stopOpacity="0.8"
                  />
                  <stop
                    offset="100%"
                    stopColor="currentColor"
                    stopOpacity="0.2"
                  />
                </linearGradient>
              </defs>

              {/* Background circle */}
              <circle
                cx="60"
                cy="60"
                r="50"
                fill="none"
                stroke="currentColor"
                strokeWidth="1"
                opacity="0.1"
              />

              {/* Outer rotating border */}
              <circle
                cx="60"
                cy="60"
                r="50"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeDasharray="8,8"
                opacity="0.3"
              >
                <animateTransform
                  attributeName="transform"
                  type="rotate"
                  from="0 60 60"
                  to="360 60 60"
                  dur="3s"
                  repeatCount="indefinite"
                />
              </circle>

              {/* Main redirect arrow - larger and more prominent */}
              <g transform="translate(60,60)">
                {/* Arrow shaft */}
                <rect
                  x="-20"
                  y="-3"
                  width="28"
                  height="6"
                  fill="currentColor"
                  opacity="0.9"
                  rx="3"
                >
                  <animate
                    attributeName="opacity"
                    values="0.7;1;0.7"
                    dur="1.5s"
                    repeatCount="indefinite"
                  />
                </rect>

                {/* Arrow head */}
                <polygon points="8,-8 8,8 20,0" fill="currentColor" opacity="1">
                  <animate
                    attributeName="opacity"
                    values="0.8;1;0.8"
                    dur="1.5s"
                    repeatCount="indefinite"
                  />
                  <animateTransform
                    attributeName="transform"
                    type="translate"
                    values="0,0; 3,0; 0,0"
                    dur="1.5s"
                    repeatCount="indefinite"
                  />
                </polygon>
              </g>

              {/* Enhanced moving dots to show direction */}
              <circle cx="25" cy="60" r="3" fill="currentColor" opacity="0.7">
                <animate
                  attributeName="cx"
                  values="25;95;25"
                  dur="2.5s"
                  repeatCount="indefinite"
                />
                <animate
                  attributeName="opacity"
                  values="0;0.9;0"
                  dur="2.5s"
                  repeatCount="indefinite"
                />
              </circle>

              <circle cx="20" cy="60" r="2.5" fill="currentColor" opacity="0.5">
                <animate
                  attributeName="cx"
                  values="20;90;20"
                  dur="2.5s"
                  begin="0.4s"
                  repeatCount="indefinite"
                />
                <animate
                  attributeName="opacity"
                  values="0;0.7;0"
                  dur="2.5s"
                  begin="0.4s"
                  repeatCount="indefinite"
                />
              </circle>

              <circle cx="15" cy="60" r="2" fill="currentColor" opacity="0.4">
                <animate
                  attributeName="cx"
                  values="15;85;15"
                  dur="2.5s"
                  begin="0.8s"
                  repeatCount="indefinite"
                />
                <animate
                  attributeName="opacity"
                  values="0;0.5;0"
                  dur="2.5s"
                  begin="0.8s"
                  repeatCount="indefinite"
                />
              </circle>
            </svg>
          </div>
          <div className="mt-2 bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg">
            <div className="flex flex-col items-center space-y-3">
              {/* Redirect message */}
              <div className="text-center">
                <p className="text-md font-medium text-foreground">
                  Redirecting to confirmation page
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  Please wait {countdown} second{countdown !== 1 ? "s" : ""}...
                </p>
              </div>
            </div>
          </div>
        </div>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default AlreadySubmittedModal;
