import React from "react";
import {
  AlertDialog,
  AlertDialog<PERSON>ction,
  AlertDialogContent,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { AlertCircle } from "lucide-react";
import { useAtom } from "jotai";
import { staticContentsAtom } from "@/lib/atom";

interface ErrorNotificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  message?: string;
  buttonText?: string;
}

const ErrorNotificationModal: React.FC<ErrorNotificationModalProps> = ({
  isOpen,
  onClose,
  title = "Error",
  message = "An error occurred. Please try again.",
  buttonText = "OK",
}) => {
  const [staticContents] = useAtom<any>(staticContentsAtom);
  if (!isOpen) return null;
  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent className="max-w-md border-0 shadow-lg">
        <div className="bg-error h-2 w-full absolute top-0 left-0 rounded-t-lg"></div>
        <AlertDialogHeader className="flex flex-col items-center pt-6">
          <div className="w-16 h-16 rounded-full bg-error/10 flex items-center justify-center mb-4">
            <AlertCircle className="h-8 w-8 text-error" />
          </div>
          <AlertDialogTitle className="text-xl font-semibold text-center text-gray-800">
            {staticContents?.application?.ocr_error|| title}
          </AlertDialogTitle>
          <div className="mt-2 text-center text-gray-600">{staticContents?.application?.ocr_error_issue|| message}</div>
        </AlertDialogHeader>
        <AlertDialogFooter className="flex justify-center sm:justify-center mt-4">
          <AlertDialogAction
            onClick={onClose}
            className="bg-error hover:bg-error/90 text-white font-medium px-8"
          >
            {buttonText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default ErrorNotificationModal;
