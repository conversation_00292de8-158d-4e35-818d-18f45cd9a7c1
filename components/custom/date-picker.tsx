import React, { useEffect, useState } from "react";
import { format } from "date-fns";
import { de } from "date-fns/locale";
import { CalendarIcon, Loader2 } from "lucide-react";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { FieldTitle } from "./FieldTitle";
import { useAtom } from "jotai";
import {
  dateReplacement,
  preferredDateFormat,
  preferredLanguage,
  staticContentsAtom,
} from "@/lib/atom";

export default function DatePicker(props: any) {
  const {
    field,
    selectedValue,
    register,
    label,
    isMandatory,
    handleChange,
    placeholder,
    disabled,
    errorMessage,
    setError,
    watch,
    clearErrors,
    fieldName,
    validateOn,
    validateWith,
  } = props;
  const [calendarOpen, setCalendarOpen] = useState(false);
  const tenYearsAgo = new Date();
  tenYearsAgo.setFullYear(tenYearsAgo.getFullYear() - 10);
  const [preferredFormat] = useAtom(preferredDateFormat);
  const [dateReplace] = useAtom(dateReplacement);
  const [preferLang] = useAtom(preferredLanguage);
  const [staticContent] = useAtom<any>(staticContentsAtom);

  useEffect(() => {
    if (disabled && selectedValue) {
      try {
        // Parse the date string properly
        const formattedDate = formatDateForDisplay(selectedValue);
        if (formattedDate) {
          const date = new Date(formattedDate);
          if (!isNaN(date.getTime())) {
            handleDateSelect(date);
          }
        }
      } catch (error) {
        console.error("Error handling disabled date:", error);
      }
    }
  }, []);

  function isValidDate(dateString: string) {
    const date = new Date(dateString);
    return !isNaN(date.getTime());
  }

  const validateFutureDate = (
    fieldName: any,
    selectedDate: any,
    currentDate: any,
    label: any
  ) => {
    if (selectedDate && new Date(selectedDate) > currentDate) {
      setError(fieldName, {
        type: "manual",
        message:
          staticContent?.errors?.dateValidation?.futureDateNotAllowed?.replace(
            "${label}",
            label
          ) || `${label} cannot be in the future`,
      });
    } else {
      clearErrors(fieldName);
    }
  };

  function validatePastDate(
    fieldName: any,
    selectedDate: any,
    currentDate: any,
    label: any
  ) {
    if (selectedDate && new Date(selectedDate) < currentDate) {
      setError(fieldName, {
        type: "manual",
        message:
          staticContent?.errors?.dateValidation?.pastDateNotAllowed?.replace(
            "${label}",
            label
          ) || `${label} cannot be in the past`,
      });
    } else {
      clearErrors(fieldName);
    }
  }
  const isAtLeast10YearsOld = (date: any) => {
    if (!date) return true;
    const tenYearsAgo = new Date();
    tenYearsAgo.setFullYear(tenYearsAgo.getFullYear() - 10);
    return new Date(date) <= tenYearsAgo;
  };

  const isAtLeast16YearsOld = (date: any) => {
    if (!date) return true;
    const sixteenYearsAgo = new Date();
    sixteenYearsAgo.setFullYear(sixteenYearsAgo.getFullYear() - 16);
    return new Date(date) <= sixteenYearsAgo;
  };

  const handleDateValidation = (
    fieldName: any,
    validateOn: any,
    validateWith: any
  ) => {
    const selectedDate = watch(fieldName);

    const validateDateWith = watch(
      fieldName.replace(/^((?:[^.]*\.){2})[^.]+/, `$1${validateWith}`)
    );

    var validateDate = new Date(validateDateWith);

    const currentDate = new Date();
    if (validateDateWith && validateWith) {
      if (new Date(selectedDate) < validateDate) {
        setError(fieldName, {
          type: "custom",
          message:
            staticContent?.errors?.dateValidation?.endDateBeforeStartDate?.replace(
              "${label}",
              label
            ) || `${label} Date cannot be less than the Start Date`,
        });
      } else if (validateOn === "futureDate") {
        validateFutureDate(fieldName, selectedDate, currentDate, label);
      } else if (validateOn === "pastDate") {
        validatePastDate(fieldName, selectedDate, currentDate, label);
      } else if (validateOn === "minimum_10_years") {
        if (!isAtLeast10YearsOld(selectedDate)) {
          setError(fieldName, {
            type: "custom",
            message:
              staticContent?.errors?.dateValidation?.minimumAge10?.replace(
                "${label}",
                label
              ) || `${label} Date must be at least 10 years old`,
          });
        } else {
          // console.log("current date", currentDate);
        }
      } else if (validateOn === "minimum_16_years") {
        if (!isAtLeast16YearsOld(selectedDate)) {
          setError(fieldName, {
            type: "custom",
            message:
              staticContent?.errors?.dateValidation?.minimumAge16?.replace(
                "${label}",
                label
              ) || `${label} Date must be at least 16 years old`,
          });
        }
      }
    } else if (validateOn === "futureDate") {
      validateFutureDate(fieldName, selectedDate, currentDate, label);
    } else if (validateOn === "pastDate") {
      validatePastDate(fieldName, selectedDate, currentDate, label);
    } else if (validateOn === "minimum_10_years") {
      if (!isAtLeast10YearsOld(selectedDate)) {
        setError(fieldName, {
          type: "custom",
          message: `${label} must be atleast 10 years old`,
        });
      } else {
        // console.log("current date", currentDate);
      }
    } else if (validateOn === "minimum_16_years") {
      if (!isAtLeast16YearsOld(selectedDate)) {
        setError(fieldName, {
          type: "custom",
          message: `${label} must be at least 16 years old`,
        });
      }
    }
  };

  const applyReplacements = (formatString: any, replacements: any) => {
    let updatedFormat = formatString;
    replacements.forEach((replacement: any) => {
      const [from, to] = Object.entries(replacement)[0];
      updatedFormat = updatedFormat.replaceAll(from, to);
    });
    return updatedFormat;
  };

  const handleDateSelect = (date: any) => {
    if (!date) return;

    // Ensure we're working with a Date object
    const selectedDate = new Date(date);

    // Format the date in YYYY-MM-DD format
    const year = selectedDate.getFullYear();
    const month = String(selectedDate.getMonth() + 1).padStart(2, "0");
    const day = String(selectedDate.getDate()).padStart(2, "0");

    const formattedDate = `${year}-${month}-${day}`;
    handleChange(formattedDate);
  };

  const formatDateForDisplay = (dateString: string): Date | undefined => {
    if (!dateString || dateString === "") return undefined;

    try {
      // Parse the date string and create a new Date object
      const [year, month, day] = dateString.split("-").map(Number);
      const date = new Date(year, month - 1, day);

      // Check if the date is valid
      if (isNaN(date.getTime())) return undefined;

      return date;
    } catch (error) {
      console.error("Error formatting date:", error);
      return undefined;
    }
  };

  useEffect(() => {
    handleDateValidation(fieldName, validateOn, validateWith);
  }, [selectedValue, fieldName, validateOn, validateWith]);

  return (
    <>
      <div className="w-full max-h-min" {...register}>
        <FieldTitle label={label} isMandatory={isMandatory} />
        <Popover
          open={calendarOpen}
          onOpenChange={(val) => {
            if (!disabled) setCalendarOpen(val);
          }}
        >
          <PopoverTrigger asChild>
            <Button
              variant={"outline"}
              className={cn(
                " pl-3 text-left font-normal border border-border w-full rounded focus:bg-white",
                !selectedValue && "text-muted-foreground",
                disabled &&
                  "pointer-events-none cursor-none bg-gray-300 opacity-50"
              )}
            >
              {selectedValue &&
              selectedValue !== "" &&
              preferredFormat &&
              isValidDate(selectedValue) ? (
                format(
                  formatDateForDisplay(selectedValue) ||
                    new Date(selectedValue),
                  applyReplacements(
                    preferredFormat,
                    dateReplace?.length
                      ? dateReplace
                      : [
                          { DD: "dd" },
                          { MM: "MMM" },
                          { YY: "yyyy" },
                          { "/": " " },
                        ]
                  ),
                  { locale: preferLang === "de" ? de : undefined }
                )
              ) : (
                <span>{placeholder}</span>
              )}
              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent
            className="w-auto p-0  bg-white m-0 rounded "
            align="start"
          >
            <Calendar
              locale={preferLang === "de" ? de : undefined}
              captionLayout="dropdown-buttons"
              mode="single"
              onSelect={(date: any) => {
                handleDateSelect(date);
                setCalendarOpen(false);
              }}
              selected={formatDateForDisplay(selectedValue)}
              defaultMonth={formatDateForDisplay(selectedValue)}
              disabled={(date) => {
                if (validateOn === "futureDate") {
                  return date > new Date();
                }
                if (validateOn === "pastDate") {
                  date < new Date();
                }
                if (validateOn === "minimum_10_years") {
                  return date > tenYearsAgo;
                }
                if (validateOn === "minimum_16_years") {
                  const sixteenYearsAgo = new Date();
                  sixteenYearsAgo.setFullYear(
                    sixteenYearsAgo.getFullYear() - 16
                  );
                  return date > sixteenYearsAgo;
                }
                return false;
              }}
              fromYear={
                validateOn === "pastDate" ? new Date().getFullYear() : 1900
              }
              toMonth={
                validateOn === "minimum_16_years"
                  ? new Date(new Date().getFullYear() - 16, 2)
                  : validateOn === "minimum_10_years"
                  ? tenYearsAgo
                  : validateOn === "futureDate"
                  ? new Date()
                  : new Date(new Date().getFullYear() + 50, 11)
              }
              toYear={
                validateOn === "minimum_16_years"
                  ? new Date().getFullYear() - 16
                  : validateOn === "minimum_10_years"
                  ? new Date().getFullYear() - 10
                  : validateOn === "futureDate"
                  ? new Date().getFullYear()
                  : new Date().getFullYear() + 50
              }
              initialFocus
            />
          </PopoverContent>
        </Popover>
      </div>
    </>
  );
}
