import React from "react";
import ReactMarkdown from "react-markdown";
import DynamicFields from "./DynamicFields";
import { AlertBox } from "./alert-dialog";
import { <PERSON>Renderer } from "./linkRender";
import { useFile } from "@/hooks/useFile";
import { applicationId, email, nextForm } from "@/lib/atom";
import { useAtom } from "jotai";

interface FormContainerProps {
  arrayIndex?: number;
  errors?: any;
  fieldData?: any;
  handleAddMore?: Function;
  watch?: Function;
  getLookupData?: Function;
  register?: any;
  setValue?: any;
  clearErrors?: any;
  uploadDocs?: Function;
  studentDetail?: any;
  handleDeleteFile?: Function;
  documents?: any;
  studentDetails: any;
  trigger?: any;
  setError?: any;
  handleRemove?: any;
  subSectionArray?: any;
  subSectionArrayMap?: Record<string, number[]>;
  popupDetails?: any;
  formQuery?: any;
  enableAutoDetection?: boolean;
}

export default function FormContainer({
  documents,
  handleDeleteFile,
  studentDetail,
  setValue,
  arrayIndex = -1,
  errors,
  fieldData,
  watch = () => { },
  handleAddMore = () => { },
  getLookupData,
  register,
  clearErrors,
  uploadDocs,
  trigger,
  studentDetails,
  setError,
  handleRemove,
  subSectionArray,
  subSectionArrayMap = {},
  popupDetails,
  formQuery,
  enableAutoDetection,
  ...rest
}: FormContainerProps) {
  const { deleteFile } = useFile();
  const [userEmail] = useAtom(email);
  const [applicantId] = useAtom(applicationId);
  const [nextFormDetails] = useAtom<any>(nextForm);

  const checkVisibility = (visibleWhenProps: any) => {
    if (!visibleWhenProps) {
      return true;
    }
    const fieldWatchValue = watch(visibleWhenProps.fieldName);
    let isArr = Array.isArray(fieldWatchValue);
    const { condition, value } = visibleWhenProps;

    if (
      Array.isArray(visibleWhenProps?.value) &&
      condition !== "notEqual" &&
      condition !== "equal"
    ) {
      return visibleWhenProps?.value.some((item: string) => {
        if (typeof fieldWatchValue === "string") {
          return item === (fieldWatchValue as string);
        }

        if (typeof fieldWatchValue === "object" && fieldWatchValue !== null) {
          return (
            item === fieldWatchValue?.value || item === fieldWatchValue?.label
          );
        }

        if (Array.isArray(fieldWatchValue)) {
          return fieldWatchValue.some(
            (v: any) => item === v?.value || item === v?.label || item === v
          );
        }

        return false;
      });
    }

    if (
      (visibleWhenProps.condition === "notAnd" ||
        visibleWhenProps.condition === "and") &&
      Array.isArray(visibleWhenProps.rules)
    ) {
      const allRulesTrue = visibleWhenProps.rules.every((rule: any) => {
        const value = watch(rule.fieldName);
        const isArr = Array.isArray(value);

        if (typeof value === "object" && !isArr) {
          return value?.value === rule.value || value?.label === rule.value;
        }

        if (isArr) {
          return value.some(
            (v: any) =>
              v?.value === rule.value ||
              v?.label === rule.value ||
              v === rule.value
          );
        }

        return value === rule.value;
      });

      return visibleWhenProps.condition === "notAnd"
        ? !allRulesTrue
        : allRulesTrue;
    }

    if (condition === "exists") {
      return Array.isArray(fieldWatchValue)
        ? fieldWatchValue.length > 0
        : !!fieldWatchValue;
    }

    if (condition === "notExists") {
      return Array.isArray(fieldWatchValue)
        ? fieldWatchValue.length > 0
        : !fieldWatchValue;
    }

    if (typeof fieldWatchValue == "object" && !isArr) {
      if (condition === "notEqual") {
        return !value.some(
          (item: { value: string }) => item.value === fieldWatchValue?.value
        );
      }
      if (condition === "equal") {
        return value.some(
          (item: { value: string }) => item.value === fieldWatchValue?.value
        );
      }
      if (fieldWatchValue?.value == visibleWhenProps?.value) return true;
      return false;
    }
    if (visibleWhenProps?.value) {
      if (condition === "notEqual") {
        return !(
          visibleWhenProps?.value.some(
            (item: { value: string }) =>
              item.value === fieldWatchValue || item === fieldWatchValue
          ) ||
          (isArr &&
            fieldWatchValue.some((obj: any) =>
              value.some((item: { value: string }) => item.value === obj.value)
            )) ||
          (Array.isArray(visibleWhenProps?.value) &&
            visibleWhenProps?.value.includes(fieldWatchValue))
        );
      }
      if (condition === "equal") {
        return (
          visibleWhenProps?.value.some(
            (item: { value: string }) =>
              item.value === fieldWatchValue || item === fieldWatchValue
          ) ||
          (isArr &&
            fieldWatchValue.some((obj: any) =>
              value.some((item: { value: string }) => item.value === obj.value)
            ))
        );
      }
      return fieldWatchValue === visibleWhenProps?.value ||
        (isArr &&
          fieldWatchValue?.some(
            (obj: any) => obj.value === visibleWhenProps?.value
          ))
        ? true
        : false;
    }
    return true;
  };

  const handleDeleteFiles = async (payload: any) => {
    await deleteFile({
      payload: payload?.fileData,
      studentDetail: {
        email: userEmail,
        oapName: nextFormDetails?.oap,
        applicationId: applicantId,
        name: payload?.documentName,
        type: payload?.documentType,
        documentId: payload?.documentId,
      },
    });
  };

  return (
    <div className="mt-5 ml-1 mr-1">
      {fieldData?.map((fieldItem: any, fieldIndex: number) => {
        if (fieldItem?.sectionCanRepeat) {
          // Get the array for this specific field
          const fieldArray =
            subSectionArrayMap[fieldItem.fieldName] || subSectionArray || [];

          return (
            <div
              key={`${fieldItem.fieldName}_section_${fieldIndex}_${arrayIndex}`}
            >
              {fieldArray.map((i: any, subIndex: number) => (
                <div
                  key={`${fieldItem.fieldName}_subsection_${i}_${subIndex}_${arrayIndex}`}
                >
                  <DynamicFields
                    subSectionIndex={i}
                    getLookupData={getLookupData}
                    register={register}
                    arrayIndex={arrayIndex}
                    selectedValue={
                      watch(fieldItem?.fieldName) ||
                      watch(`${fieldItem?.documentType}`) ||
                      ""
                    }
                    isVisibleWhen={checkVisibility(fieldItem?.visibleWhen)}
                    fieldItem={fieldItem}
                    studentData={studentDetails}
                    label={
                      fieldItem?.label ||
                      fieldItem?.displayName ||
                      fieldItem?.placeholder
                    }
                    handleValueChanged={(value: any, type?: string) => {
                      if (!fieldItem?.fieldName) return;
                      clearErrors(fieldItem?.fieldName);
                      clearErrors(`${fieldItem?.documentType}`);
                      if (type === "file") {
                        setValue(fieldItem.fieldName, value);
                        return;
                      }

                      if (
                        fieldItem?.type === "pickList" &&
                        fieldItem?.fieldDisplayName
                      ) {
                        setValue(
                          fieldItem?.fieldDisplayName,
                          typeof value === "object" ? value.label : value
                        );
                      }
                      if (fieldItem?.resetChild) {
                        if (Array.isArray(fieldItem.resetChild)) {
                          fieldItem?.resetChild.forEach((child: any) => {
                            setValue(child, "");
                            setValue(`${child}DisplayName`, "");
                            clearErrors(child);
                          });
                        } else {
                          setValue(
                            fieldItem?.resetChild,
                            value?.[fieldItem?.dependentFieldName] || ""
                          );
                          clearErrors(fieldItem?.resetChild);
                        }
                      }
                      setValue(fieldItem?.fieldName, value);
                    }}
                    errorMessage={
                      errors?.[fieldItem?.fieldName]?.message ||
                      errors?.[`${fieldItem?.documentType}`]?.message
                    }
                    name={fieldItem?.fieldName}
                    uploadDocs={uploadDocs}
                    handleDeleteFile={handleDeleteFile}
                    trigger={trigger}
                    watch={watch}
                    clearErrors={clearErrors}
                    setValue={setValue}
                    setError={setError}
                    handleRemove={handleRemove}
                    {...rest}
                    enableAutoDetection={enableAutoDetection}
                    isOcrReprocess={formQuery.isOcrReprocess}
                    maxOcrReprocessCount={formQuery.maxOcrReprocess}
                  />
                </div>
              ))}

              {errors[fieldItem.requiredWhenFieldName] && (
                <span className="text-sm text-[red] h-0">
                  {errors[fieldItem.requiredWhenFieldName].message}
                </span>
              )}

              {fieldArray.length < fieldItem?.maxLength &&
                checkVisibility(fieldItem?.addOnButtonDetails?.visibleWhen) && (
                  <div
                    className="text-background rounded  bg-slate-900/90  hover:bg-primary font-bold  text-sm px-5 py-2.5 mt-5 me-2 mb-5 cursor-pointer text-center"
                    onClick={() => {
                      handleAddMore(fieldItem);
                    }}
                  >
                    <p>{fieldItem?.addOnButtonDetails?.placeHolder}</p>
                  </div>
                )}
            </div>
          );
        }

        return (
          <div key={`${fieldItem.fieldName}_field_${fieldIndex}_${arrayIndex}`}>
            <DynamicFields
              getLookupData={getLookupData}
              register={register}
              arrayIndex={arrayIndex}
              selectedValue={
                watch(fieldItem?.fieldName) ||
                watch(`${fieldItem?.documentType}`) ||
                ""
              }
              isVisibleWhen={checkVisibility(fieldItem?.visibleWhen)}
              fieldItem={fieldItem}
              studentData={studentDetails}
              label={
                fieldItem?.label ||
                fieldItem?.displayName ||
                fieldItem?.placeholder
              }
              handleValueChanged={(value: any, type?: string) => {
                if (!fieldItem?.fieldName) return;
                clearErrors(fieldItem?.fieldName);
                clearErrors(`${fieldItem?.documentType}`);
                if (type === "file") {
                  setValue(fieldItem.fieldName, value);
                  return;
                }
                if (fieldItem.fieldName === "addMore") {
                  if (
                    fieldItem.type === "radio" &&
                    value?.toLoweCase() === "yes"
                  ) {
                    handleAddMore();
                  }
                }

                if (
                  fieldItem.type === "pickList" &&
                  fieldItem?.fieldDisplayName
                ) {
                  setValue(
                    fieldItem?.fieldDisplayName,
                    typeof value === "object" ? value.label : value
                  );
                  if (fieldItem?.mapProductId) {
                    setValue(
                      fieldItem?.mapProductId,
                      value?.[fieldItem?.mapProductId]
                    );
                  }
                }
                if (fieldItem?.resetChild) {
                  if (Array.isArray(fieldItem.resetChild)) {
                    fieldItem?.resetChild.forEach((child: any) => {
                      if (fieldItem?.isDocumentResetChild) {
                        let documentDetails =
                          studentDetails[child] || watch(child);

                        documentDetails = Array.isArray(documentDetails)
                          ? documentDetails[0]
                          : documentDetails;

                        if (documentDetails && documentDetails?.documentId) {
                          handleDeleteFiles(documentDetails);
                        }
                      }
                      setValue(child, "");
                      setValue(`${child}DisplayName`, "");
                      clearErrors(child);
                    });
                  } else {
                    setValue(
                      fieldItem?.resetChild,
                      value?.[fieldItem?.dependentFieldName] || ""
                    );
                    clearErrors(fieldItem?.resetChild);
                  }
                }
                if (fieldItem.subFieldName) {
                  setValue(fieldItem?.subFieldName, value);
                }
                if (fieldItem?.resetWithCondition) {
                  const { condition, dependentFieldName } =
                    fieldItem.resetWithCondition;

                  if (value === condition.value) {
                    const dependentFieldValues = watch(dependentFieldName);
                    if (typeof dependentFieldValues === "object") {
                      for (const key in dependentFieldValues) {
                        setValue(key, dependentFieldValues[key]);
                        clearErrors(key);
                      }
                    }
                  }
                }
                setValue(fieldItem?.fieldName, value);
              }}
              errorMessage={
                errors?.[fieldItem?.fieldName]?.message ||
                errors?.[`${fieldItem?.documentType}`]?.message
              }
              name={fieldItem?.fieldName}
              uploadDocs={uploadDocs}
              handleDeleteFile={handleDeleteFile}
              trigger={trigger}
              watch={watch}
              clearErrors={clearErrors}
              setError={setError}
              setValue={setValue}
              isOcrReprocess={formQuery.isOcrReprocess}
              maxOcrReprocessCount={formQuery.maxOcrReprocess}
              {...rest}
              enableAutoDetection={enableAutoDetection}
            />
          </div>
        );
      })}
      {popupDetails && (
        <AlertBox
          title={popupDetails?.title}
          placeholder={popupDetails?.placeholder}
        >
          <div className="prose dark:prose-invert max-w-none mb-6 text-sm">
            <ReactMarkdown
              className="markDown my-4"
              components={{ a: LinkRenderer }}
            >
              {popupDetails?.text}
            </ReactMarkdown>
          </div>
        </AlertBox>
      )}
    </div>
  );
}
