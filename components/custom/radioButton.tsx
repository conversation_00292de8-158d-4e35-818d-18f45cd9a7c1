import { Label } from "@/components/ui/label";
import * as RadioGroup from "@radix-ui/react-radio-group";

interface RadioButtonProps {
  selectedValue: string;
  handleChange: Function;
  register: any;
  id: string;
  options?: Array<{ value: string; label: string }> | Array<string>;
  defaultValue?: any;
  errorMessage?: any;
  isLoading?: boolean;
}

export function RadioButton(props: RadioButtonProps) {
  const {
    handleChange,
    register,
    options,
    selectedValue,
    errorMessage,
    isLoading,
  } = props;
  const { onChange, onBlur, ref } = register;

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <RadioGroup.Root
      className="RadioGroupRoot"
      value={selectedValue}
      aria-label="View density"
      onValueChange={(value: any) => handleChange(value)}
      onClick={(e) => e.preventDefault()}
      onBlur={onBlur}
      onChange={onChange}
    >
      {options?.map((item: any, index: number) => (
        <div key={index} style={{ display: "flex", alignItems: "center" }}>
          <RadioGroup.Item
            className={`RadioGroupItem border ${
              errorMessage ? "border-error" : "border-border"
            }  rounded-full h-4 w-4 mx-2`}
            value={item.value || item}
            id={`r${index}`}
          >
            <RadioGroup.Indicator className="RadioGroupIndicator border border-border rounded-full" />
          </RadioGroup.Item>
          <Label className={`Label`} htmlFor={`r${index}`}>
            {item.displayText || item.label || item}
          </Label>
        </div>
      ))}
    </RadioGroup.Root>
  );
}
