"use client";
import IntlTelInput from "react-intl-tel-input";
import "react-intl-tel-input/dist/main.css";
import "./mobile-input.css";
import { useState, useEffect, useRef } from "react";
import { useLocationDetection } from "@/hooks/useLocationDetection";

interface MobileInputProps {
  handleChange?: Function;
  selectedValue: any;
  register?: any;
  onBlur?: any;
  handleCountry?: any;
  defaultCountry?: string;
  errorMessage?: string;
  placeholder?: string;
  handleFlagChange?: any;
  enableAutoDetection?: boolean;
  onAutoDetectionComplete?: (countryCode: string, method: string) => void;
}

export function MobileInput({
  selectedValue,
  handleChange = () => {},
  register,
  onBlur,
  defaultCountry,
  errorMessage,
  placeholder,
  handleFlagChange,
  enableAutoDetection,
  onAutoDetectionComplete,
}: MobileInputProps) {
  const [selectedFlag, setSelectedFlag] = useState<any>();
  const [autoDetectedCountry, setAutoDetectedCountry] = useState<string | null>(
    null
  );
  const callbackCalledRef = useRef<string | null>(null);

  const {
    countryCode: detectedCountryCode,
    isDetecting,
    isSuccess,
    method,
    error,
  } = useLocationDetection({
    autoDetect: enableAutoDetection,
    defaultCountry: defaultCountry,
  });

  useEffect(() => {
    if (
      enableAutoDetection &&
      !isDetecting &&
      detectedCountryCode &&
      isSuccess
    ) {
      const hasExistingPhoneData = selectedValue && selectedValue.trim() !== "";
      const hasExistingCountry = !!defaultCountry && defaultCountry !== "us";

      if (
        !selectedFlag &&
        !hasExistingPhoneData &&
        !hasExistingCountry &&
        autoDetectedCountry !== detectedCountryCode &&
        callbackCalledRef.current !== detectedCountryCode
      ) {
        setAutoDetectedCountry(detectedCountryCode);
        if (onAutoDetectionComplete && method) {
          onAutoDetectionComplete(detectedCountryCode, method);
          callbackCalledRef.current = detectedCountryCode;
        }
      }
    }
  }, [
    enableAutoDetection,
    isDetecting,
    detectedCountryCode,
    selectedFlag,
    autoDetectedCountry,
    isSuccess,
    method,
    selectedValue,
    defaultCountry,
  ]);

  return (
    <div {...register}>
      <IntlTelInput
        key={autoDetectedCountry || selectedFlag || defaultCountry || "us"}
        value={selectedValue}
        containerClassName={`intl-tel-input w-full border ${
          errorMessage ? "border-error" : "border-border"
        }  rounded ${isDetecting ? "opacity-75" : ""}`}
        inputClassName="form-control w-full border-border rounded h-[38px]"
        telInputProps={{
          required: true,
          disabled: isDetecting,
          "aria-label": placeholder,
        }}
        separateDialCode
        onPhoneNumberChange={(
          status,
          phoneNumber,
          country,
          formattedNumber
        ) => {
          handleChange(status, phoneNumber, country, formattedNumber);
        }}
        defaultCountry={
          autoDetectedCountry || selectedFlag || defaultCountry || "us"
        }
        onSelectFlag={(
          _currentNumber,
          selectedCountryData,
          _fullNumber,
          _isValid
        ) => {
          setSelectedFlag(selectedCountryData?.iso2);
          setAutoDetectedCountry(null);
          handleFlagChange();
        }}
        onPhoneNumberBlur={onBlur}
        placeholder={placeholder}
      />
    </div>
  );
}
