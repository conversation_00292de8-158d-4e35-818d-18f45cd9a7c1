import React, { useEffect, useState, useMemo } from "react";
import { useAtom } from "jotai";
import { staticContentsAtom } from "@/lib/atom";

export type AuthenticationPhase =
  | "initializing"
  | "validating_session"
  | "processing_credentials"
  | "fetching_user_data"
  | "preparing_dashboard"
  | "redirecting";

interface DynamicAuthProgressProps {
  isIframe: boolean;
  currentPhase?: AuthenticationPhase; // External control of current phase
  currentOperation?: string; // Optional prop to override current operation
}

const DynamicAuthProgress: React.FC<DynamicAuthProgressProps> = ({
  isIframe,
  currentPhase = "initializing",
  currentOperation,
}) => {
  const [staticContents] = useAtom<any>(staticContentsAtom);
  const [progress, setProgress] = useState(0);
  const [currentOperationText, setCurrentOperationText] = useState("");

  // Define user-friendly authentication phases
  const authPhases = useMemo(
    () => ({
      initializing: {
        title:
          staticContents?.authentication?.initializing_title ||
          "Getting Started",
        description: isIframe
          ? staticContents?.authentication?.initializing_iframe ||
            "Connecting to your account..."
          : staticContents?.authentication?.initializing_direct ||
            "Setting things up for you...",
        operation: isIframe
          ? "Connecting to your account"
          : "Checking if you're already signed in",
        percentage: 15,
      },
      validating_session: {
        title:
          staticContents?.authentication?.validating_title ||
          "Verifying Your Identity",
        description: isIframe
          ? staticContents?.authentication?.validating_iframe ||
            "Confirming your account details..."
          : staticContents?.authentication?.validating_direct ||
            "Making sure everything looks good...",
        operation: isIframe
          ? "Confirming your account details"
          : "Verifying your login status",
        percentage: 35,
      },
      processing_credentials: {
        title:
          staticContents?.authentication?.processing_title || "Signing You In",
        description: isIframe
          ? staticContents?.authentication?.processing_iframe ||
            "Securely logging you into your account..."
          : staticContents?.authentication?.processing_direct ||
            "Getting you signed in safely...",
        operation: isIframe
          ? "Logging you in securely"
          : "Completing your sign-in",
        percentage: 55,
      },
      fetching_user_data: {
        title:
          staticContents?.authentication?.fetching_title ||
          "Loading Your Information",
        description:
          staticContents?.authentication?.fetching_desc ||
          "Getting your application details ready...",
        operation: "Loading your application information",
        percentage: 75,
      },
      preparing_dashboard: {
        title:
          staticContents?.authentication?.preparing_title ||
          "Preparing Your Dashboard",
        description:
          staticContents?.authentication?.preparing_desc ||
          "Setting up your personalized experience...",
        operation: "Customizing your dashboard",
        percentage: 90,
      },
      redirecting: {
        title:
          staticContents?.authentication?.redirecting_title || "Almost Ready!",
        description:
          staticContents?.authentication?.redirecting_desc ||
          "Taking you to your application now...",
        operation: "Opening your application",
        percentage: 100,
      },
    }),
    [staticContents, isIframe]
  );

  // Update progress when phase changes externally
  useEffect(() => {
    if (authPhases[currentPhase]) {
      setProgress(authPhases[currentPhase].percentage);
      setCurrentOperationText(authPhases[currentPhase].operation);
    }
  }, [currentPhase, authPhases]);

  const currentPhaseData = authPhases[currentPhase];
  const displayOperation = currentOperation || currentOperationText;

  return (
    <div className="text-white text-center max-w-md mx-auto">
      {/* Main Title */}
      <h1 className="text-2xl mb-6 font-semibold">{currentPhaseData.title}</h1>

      {/* Animated Icon */}
      <div className="mb-6 flex justify-center">
        <div className="relative">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 100 100"
            width="80"
            height="80"
            className="text-white"
          >
            <defs>
              <linearGradient
                id="authGradient"
                x1="0%"
                y1="0%"
                x2="100%"
                y2="100%"
              >
                <stop offset="0%" stopColor="currentColor" stopOpacity="0.8" />
                <stop
                  offset="100%"
                  stopColor="currentColor"
                  stopOpacity="0.3"
                />
              </linearGradient>
            </defs>

            {/* Outer rotating ring */}
            <circle
              cx="50"
              cy="50"
              r="45"
              fill="none"
              stroke="url(#authGradient)"
              strokeWidth="2"
              strokeDasharray="8,4"
              opacity="0.6"
            >
              <animateTransform
                attributeName="transform"
                type="rotate"
                from="0 50 50"
                to="360 50 50"
                dur="3s"
                repeatCount="indefinite"
              />
            </circle>

            {/* Inner pulsing circle */}
            <circle cx="50" cy="50" r="25" fill="currentColor" opacity="0.2">
              <animate
                attributeName="r"
                values="20;30;20"
                dur="2s"
                repeatCount="indefinite"
              />
              <animate
                attributeName="opacity"
                values="0.1;0.3;0.1"
                dur="2s"
                repeatCount="indefinite"
              />
            </circle>

            {/* Center icon */}
            <circle cx="50" cy="50" r="8" fill="currentColor" opacity="0.8" />
          </svg>
        </div>
      </div>

      {/* Description */}
      <p className="text-lg mb-6 text-white/90 leading-relaxed">
        {currentPhaseData.description}
      </p>

      {/* Progress Bar */}
      <div className="mb-4">
        <div className="w-full bg-white/20 h-2 rounded-full overflow-hidden">
          <div
            className="bg-white h-full transition-all duration-300 ease-out rounded-full"
            style={{ width: `${progress}%` }}
          />
        </div>
      </div>

      {/* Progress Info */}
      <div className="flex justify-center items-center text-sm text-white/70 mb-4">
        <span>{Math.round(progress)}% complete</span>
      </div>

      {/* Current Operation Indicator */}
      <div className="text-xs text-white/60 uppercase tracking-wider mb-4">
        {currentPhase === "initializing" && "Getting Started"}
        {currentPhase === "validating_session" && "Verifying Identity"}
        {currentPhase === "processing_credentials" && "Signing In"}
        {currentPhase === "fetching_user_data" && "Loading Information"}
        {currentPhase === "preparing_dashboard" && "Preparing Dashboard"}
        {currentPhase === "redirecting" && "Opening Application"}
      </div>

      {/* Operation Status */}
      <div className="text-sm text-white/80 mb-4 font-medium">
        {displayOperation}
      </div>

      {/* Contextual Tips based on current phase */}
      {currentPhase === "initializing" && (
        <div className="text-xs text-white/50 italic">
          {isIframe
            ? "We're connecting to your account safely and securely..."
            : "Just a moment while we get everything ready for you..."}
        </div>
      )}

      {currentPhase === "validating_session" && (
        <div className="text-xs text-white/50 italic">
          {isIframe
            ? "Making sure your account information is correct..."
            : "Double-checking your login details..."}
        </div>
      )}

      {currentPhase === "processing_credentials" && (
        <div className="text-xs text-white/50 italic">
          We&apos;re signing you in securely - this keeps your information
          safe...
        </div>
      )}

      {currentPhase === "fetching_user_data" && (
        <div className="text-xs text-white/50 italic">
          Getting all your application information ready for you...
        </div>
      )}

      {currentPhase === "preparing_dashboard" && (
        <div className="text-xs text-white/50 italic">
          Almost there! We&apos;re setting up your personalized view...
        </div>
      )}
    </div>
  );
};

export default DynamicAuthProgress;
