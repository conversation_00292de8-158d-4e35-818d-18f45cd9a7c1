"use client";
import Image from "next/image";
import React, { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { sortOrder } from "@/helpers/Sorting";
import { getOapDetail, getOapForm, getOapFormSections } from "@/api/api";
import loader2 from "../../public/loader2.svg";
import loader from "../../public/loader.svg";
import { useAtom } from "jotai";
import {
  consumerAPIKey,
  preferredDateFormat,
  staticContentsAtom,
  preferredLanguage,
} from "@/lib/atom";
import { useFormContext } from "react-hook-form";
import DynamicFields from "../custom/DynamicFields";
import { FormLayout } from "../custom/formLayout";

import {
  confirmResetPassword,
  type ConfirmResetPasswordInput,
} from "aws-amplify/auth";
import { getFieldNamesByFormQuery, validatePassword } from "@/lib/utils";
import { fontSizeAtom } from "@/lib/atom";
import { getBrandSpecificFontStyle } from "@/lib/brandUtils";

export default function ChangePassword() {
  const [pageDetails] = useState({
    screen: process.env.NEXT_PUBLIC_OAP_NAME,
    mode: process.env.NEXT_PUBLIC_OAP_MODE,
  });

  const [saving, setSaving] = useState<boolean>(false);
  const [apiKey, setApiKey] = useAtom(consumerAPIKey);
  const [, setPreferredDateFormat] = useAtom(preferredDateFormat);
  const [errorMessage, setErrorMessage] = useState("");
  const [staticContent] = useAtom<any>(staticContentsAtom);

  const [fontSize, setFontSize] = useAtom(fontSizeAtom);

  const searchParams = useSearchParams();

  const username: any = searchParams?.get("user_name");
  const confirmationCode: any = searchParams?.get("confirmation_code");
  const [preferLang] = useAtom(preferredLanguage);

  const router = useRouter();

  const {
    register,
    setValue,
    watch,
    formState: { errors },
    clearErrors,
    trigger,
    setError,
    getValues,
  } = useFormContext();

  const { data: pageQuery, isFetching: pageQueryFetching } = useQuery({
    queryKey: [`${pageDetails.screen}-${pageDetails.mode}`, preferLang],
    queryFn: async () => {
      let res = await getOapDetail(
        {
          name: pageDetails.screen,
          mode: pageDetails.mode,
          ...(preferLang !== "en" && { language: preferLang }),
        },
        apiKey
      );
      setPreferredDateFormat(res?.preferedDateFormat);
      setApiKey(res?.eipConsumerKey);
      if (res?.fontSize) {
        setFontSize(res.fontSize);
      }
      return res;
    },
    enabled: true,
  });

  const { data: sectionQuery, isFetching: sectionQueryFetching } = useQuery({
    queryKey: [
      `${pageQuery?.PK}-${pageQuery?.SK}-${pageQuery?.landingForm}`,
      preferLang,
    ],
    queryFn: async () => {
      let res = await getOapForm(
        {
          oap: pageQuery?.PK,
          form: pageQuery?.changePasswordForm,
          mode: pageQuery?.SK,
          ...(preferLang !== "en" && { language: preferLang }),
        },
        apiKey
      );
      return res;
    },
    enabled: !!pageQuery?.PK,
  });

  const { data: formQuery, isFetching: formQueryFetching } = useQuery({
    queryKey: [
      `${pageQuery?.PK}-${pageQuery?.SK}-${sectionQuery?.SK}-${sectionQuery?.section?.[0]?.section}`,
      preferLang,
    ],
    queryFn: async () => {
      if (!(sectionQuery?.section?.[0]?.section && sectionQuery?.SK)) return;
      let res = await getOapFormSections(
        {
          oap: pageQuery?.PK,
          mode: pageQuery?.SK,
          formName: sectionQuery?.SK,
          sectionName: sectionQuery?.section?.[0]?.section,
          ...(preferLang !== "en" && { language: preferLang }),
        },
        apiKey
      );
      return res;
    },
    enabled: !!sectionQuery?.SK,
  });

  async function handleConfirmResetPassword() {
    const getDetails = getValues();
    const fieldNames = getFieldNamesByFormQuery(formQuery);

    const isValid = await trigger(fieldNames);

    if (!isValid) return;
    setSaving((prev) => !prev);
    if (getDetails?.changePassword !== getDetails?.changeRepeatPassword) {
      setSaving((prev) => !prev);
      return setError("changeRepeatPassword", {
        message:
          staticContent?.errors?.userValidation?.passwordMismatch ||
          "Confirm password should be same",
      });
    }

    if (!validatePassword(getDetails?.changePassword)) {
      setSaving((prev) => !prev);
      setErrorMessage(
        staticContent?.errors?.userValidation?.passwordRequirements ||
          "Password must contain at least one special character, a minimum length of 8 characters, one uppercase and lowercase letter."
      );
      setTimeout(() => {
        setErrorMessage("");
      }, 5000);
      return;
    }
    try {
      await confirmResetPassword({
        username,
        confirmationCode,
        newPassword: getDetails?.changePassword,
      });
      router.push("/login");
    } catch (error: any) {
      setSaving((prev) => !prev);
      console.log("error", error.name);
      if (error.message) {
        setErrorMessage(
          staticContent?.errors?.userValidation?.[error?.name] || error?.message
        );
      }
      setTimeout(() => {
        setErrorMessage("");
      }, 5000);
      return error;
    }
  }

  if (pageQueryFetching || sectionQueryFetching || formQueryFetching) {
    return (
      <main
        className="min-h-screen bg-on-background flex flex-col items-center justify-center overflow-scroll bg-contain bg-no-repeat bg-center"
        role="main"
        aria-label="Change password main content"
      >
        <Image
          priority
          src={loader}
          height={32}
          width={32}
          alt="Follow us on Twitter"
        />
      </main>
    );
  }

  return (
    <FormLayout pageQuery={pageQuery}>
      <div
        className="w-full max-w-[480px] p-9 bg-background border border-gray-200 shadow sm:px-14 sm:py-8 md:px-20 md:py-8"
        style={{ borderRadius: 8 }}
      >
        <div className="space-y-3">
          <h5
            className="font-bold text-text-primary pb-3 dark:text-white text-center text-2xl"
            style={getBrandSpecificFontStyle(fontSize, "page-title")}
          >
            {sectionQuery?.section?.[0]?.displayName}
          </h5>

          {sortOrder(formQuery?.fieldData, "indexOrder")?.map(
            (item: any, index: any) => {
              return (
                <div key={index}>
                  <DynamicFields
                    register={register}
                    selectedValue={
                      watch(item?.fieldName) ||
                      watch(`${item?.documentType}`) ||
                      ""
                    }
                    disabled={
                      item?.disabledWhen
                        ? watch(item?.disabledWhen?.fieldName)?.label ===
                          item?.disabledWhen?.value
                        : false
                    }
                    isVisibleWhen
                    fieldItem={item}
                    label={
                      item?.label || item?.displayName || item?.placeholder
                    }
                    handleValueChanged={(value: any, type?: string) => {
                      if (item?.childField && item?.setValue) {
                        if (value?.value == item?.value) {
                          setValue(item?.childField, item?.setValue);
                          clearErrors(item?.childField);
                        } else {
                          setValue(item?.childField, "");
                        }
                      }
                      clearErrors(item?.fieldName);
                      clearErrors(`${item?.documentType}`);
                      if (type === "pickList" && item?.fieldDisplayName) {
                        setValue(item?.fieldDisplayName, value);
                      }
                      if (item?.resetChild) {
                        setValue(item?.resetChild, "");
                        clearErrors(item?.resetChild);
                      }
                      setValue(item?.fieldName, value);
                    }}
                    errorMessage={
                      errors?.[item?.fieldName]?.message ||
                      errors?.[`${item?.documentType}`]?.message
                    }
                    name={item?.fieldName}
                    trigger={trigger}
                    watch={watch}
                    clearErrors={clearErrors}
                    setError={setError}
                    displayNoTitle={false}
                    setValue={setValue}
                  />
                </div>
              );
            }
          )}

          {errorMessage && (
            <span className="text-sm text-[red] h-0">{errorMessage}</span>
          )}

          <div className="form-action w-full flex justify-center ">
            {formQuery?.fieldData
              ?.filter((item: any) => item.type == "button")
              .map((ele: any, i: number) => {
                return (
                  <button
                    key={i}
                    style={getBrandSpecificFontStyle(fontSize, "label")}
                    onClick={() => {
                      handleConfirmResetPassword();
                    }}
                    className="text-background rounded  bg-primary w-full hover:bg-secondary font-bold  text-sm px-5 py-2.5"
                  >
                    {saving ? (
                      <div className=" w-full flex items-center justify-center">
                        <Image
                          priority
                          src={loader2}
                          height={20}
                          width={20}
                          alt="Follow us on Twitter"
                        />
                      </div>
                    ) : (
                      ele?.placeholder
                    )}
                  </button>
                );
              })}
          </div>
        </div>
      </div>
    </FormLayout>
  );
}
