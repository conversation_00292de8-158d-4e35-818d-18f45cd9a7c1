<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120 120" width="120" height="120">
  <!-- Outer rotating ring with gradient -->
  <defs>
    <linearGradient id="outerRingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#b42638" stop-opacity="0.8" />
      <stop offset="50%" stop-color="#0077B5" stop-opacity="0.6" />
      <stop offset="100%" stop-color="#b42638" stop-opacity="0.3" />
    </linearGradient>
    <linearGradient id="innerRingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#0077B5" stop-opacity="0.7" />
      <stop offset="100%" stop-color="#b42638" stop-opacity="0.4" />
    </linearGradient>
    <linearGradient id="linkedinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#0077B5" />
      <stop offset="100%" stop-color="#005885" />
    </linearGradient>
  </defs>

  <!-- Rotating outer ring -->
  <circle cx="60" cy="60" r="55" fill="none" stroke="url(#outerRingGradient)" stroke-width="3" stroke-dasharray="12,6" opacity="0.8">
    <animateTransform attributeName="transform" type="rotate" from="0 60 60" to="360 60 60" dur="10s" repeatCount="indefinite" />
  </circle>

  <!-- Inner rotating ring (opposite direction) -->
  <circle cx="60" cy="60" r="45" fill="none" stroke="url(#innerRingGradient)" stroke-width="2" stroke-dasharray="8,4" opacity="0.6">
    <animateTransform attributeName="transform" type="rotate" from="360 60 60" to="0 60 60" dur="7s" repeatCount="indefinite" />
  </circle>

  <!-- LinkedIn logo background circle with pulse -->
  <circle cx="60" cy="60" r="35" fill="url(#linkedinGradient)" opacity="0.9">
    <animate attributeName="r" values="35;37;35" dur="2s" repeatCount="indefinite" />
    <animate attributeName="opacity" values="0.9;1;0.9" dur="2s" repeatCount="indefinite" />
  </circle>

  <!-- LinkedIn logo with animated movement -->
  <g>
    <!-- Complex movement animation for the entire LinkedIn logo -->
    <animateTransform attributeName="transform" type="translate"
      values="0,0; 2,-2; 3,-1; 2,1; 0,2; -2,1; -3,-1; -2,-2; 0,0"
      dur="4s" repeatCount="indefinite" />

    <!-- LinkedIn "in" text -->
    <g fill="white">
      <!-- Letter "i" -->
      <rect x="45" y="45" width="3" height="12" rx="1">
        <animate attributeName="opacity" values="1;0.8;1" dur="1.5s" repeatCount="indefinite" />
      </rect>
      <circle cx="46.5" cy="40" r="2">
        <animate attributeName="opacity" values="1;0.8;1" dur="1.5s" repeatCount="indefinite" />
      </circle>
      
      <!-- Letter "n" -->
      <path d="M52 57 L52 45 L55 45 L55 48 Q55 45 58 45 Q62 45 62 49 L62 57 L59 57 L59 50 Q59 48 57 48 Q55 48 55 50 L55 57 Z" opacity="0.95">
        <animate attributeName="opacity" values="0.95;0.75;0.95" dur="1.5s" repeatCount="indefinite" />
      </path>
    </g>

    <!-- Connecting dots animation -->
    <g>
      <circle cx="70" cy="50" r="1.5" fill="#b42638" opacity="0.8">
        <animate attributeName="opacity" values="0.8;0.3;0.8" dur="1s" repeatCount="indefinite" />
        <animate attributeName="r" values="1.5;2;1.5" dur="1s" repeatCount="indefinite" />
      </circle>
      <circle cx="75" cy="55" r="1" fill="#b42638" opacity="0.6">
        <animate attributeName="opacity" values="0.6;0.2;0.6" dur="1.2s" repeatCount="indefinite" />
        <animate attributeName="r" values="1;1.5;1" dur="1.2s" repeatCount="indefinite" />
      </circle>
      <circle cx="80" cy="60" r="1.2" fill="#b42638" opacity="0.7">
        <animate attributeName="opacity" values="0.7;0.25;0.7" dur="0.8s" repeatCount="indefinite" />
        <animate attributeName="r" values="1.2;1.8;1.2" dur="0.8s" repeatCount="indefinite" />
      </circle>
    </g>
  </g>

  <!-- Outer glow effect -->
  <circle cx="60" cy="60" r="58" fill="none" stroke="#0077B5" stroke-width="1" opacity="0.3">
    <animate attributeName="opacity" values="0.3;0.1;0.3" dur="3s" repeatCount="indefinite" />
  </circle>
</svg>
