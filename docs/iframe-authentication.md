# Iframe Authentication Flow

This document describes how to use the conditional authentication flow based on whether the application is loaded as an iframe or not.

## Overview

The application supports two authentication modes:

1. **Direct Authentication Mode** - The default mode when the application is loaded normally
2. **Iframe Authentication Mode** - Used when the application is embedded in an iframe with `isIframe=true` parameter

## How It Works

### Direct Authentication Mode

When the application is loaded without the `isIframe=true` parameter (or with `isIframe=false`):

1. The application directly checks for an existing authentication session
2. If authenticated, redirects to the application filter page
3. If not authenticated, redirects to the login page
4. No waiting for postMessage events is needed

### Iframe Authentication Mode

When the application is loaded with the query parameter `?isIframe=true`:

1. The application sets up a listener for `window.postMessage` events
2. It waits for authentication data to be received from the parent window
3. When authentication data is received, it validates the origin for security
4. It uses the received tokens to authenticate the user
5. After successful authentication, it redirects to the application filter page

## Security Considerations

The implementation includes security measures:

- Origin validation to ensure messages come from trusted sources
- Proper event listener cleanup to prevent memory leaks
- Timeout mechanism to fallback to direct authentication if no message is received

## How to Use

### Embedding the Application in an Iframe

```html
<iframe src="https://your-app-url/?isIframe=true" id="oapIframe"></iframe>
```

### Sending Authentication Data

```javascript
// Prepare authentication data
const authData = {
  type: "AUTH_REQUEST",
  access_token: "your-access-token",
  id_token: "your-id-token",
  refresh_token: "your-refresh-token",
};

// Send the data to the iframe
document
  .getElementById("oapIframe")
  .contentWindow.postMessage(authData, "https://your-app-url");
```

## Authentication Data Format

The authentication data sent via postMessage should have the following format:

```javascript
{
  type: 'AUTH_REQUEST',  // Required to identify this as an authentication request
  access_token: string,  // AWS Cognito access token
  id_token: string,      // AWS Cognito ID token
  refresh_token: string  // AWS Cognito refresh token
}
```

## Example

An example implementation of a parent application that embeds the OAP application and sends authentication data can be found in `examples/parent-app-example.html`.

## Trusted Origins

For security reasons, the application only accepts postMessage events from trusted origins. Update the `trustedOrigins` array in `app/page.tsx` to include all domains that should be allowed to send authentication data:

```javascript
const trustedOrigins = [
  "https://trusted-parent-domain.com",
  "http://localhost:3000",
];
```

## Troubleshooting

If authentication via iframe is not working:

1. Check browser console for error messages
2. Verify that the parent domain is included in the trusted origins list
3. Ensure the tokens being sent are valid and not expired
4. Confirm that the postMessage is being sent after the iframe has fully loaded
