import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const formatDate = (inputDate: any, format: any) => {
  const date = new Date(inputDate);

  // Check if the date is valid
  if (isNaN(date.getTime())) return "";

  const day = date.getDate().toString().padStart(2, "0");
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const yearFull = date.getFullYear();
  const yearShort = yearFull.toString().slice(-2);

  format = format
    .replace("DD", day)
    .replace("MM", month)
    .replace("YYYY", yearFull)
    .replace("YY", yearShort);

  return format;
};

export function validatePassword(password: any) {
  const hasUppercase = /[A-Z]/.test(password);
  const hasLowercase = /[a-z]/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
  const hasMinLength = password?.length >= 8;

  if (!hasUppercase || !hasLowercase || !hasMinLength || !hasSpecialChar) {
    return false;
  }

  return true;
}

export function getFieldNamesByFormQuery(formQuery: any) {
  const fieldNames = formQuery?.fieldData
    .filter((item: any) => item.fieldName !== undefined)
    .map((item: any) => item.fieldName);

  return fieldNames;
}

export const replacePlaceholders = (
  template: string,
  values: Record<string, string>
) => {
  return template?.replace(/{{(.*?)}}/g, (_, key) => String(values[key]) || "");
};
