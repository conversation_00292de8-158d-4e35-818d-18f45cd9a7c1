/**
 * Skip Link Utilities
 * Ensures skip links work properly according to WebAIM guidelines
 */

/**
 * Initialize skip link functionality
 * This ensures all main content areas are properly configured for skip links
 */
export function initializeSkipLinks() {
  // Ensure main content areas are focusable
  const mainContentElements = document.querySelectorAll('#maincontent, main[id="maincontent"]');
  
  mainContentElements.forEach((element) => {
    const htmlElement = element as HTMLElement;
    
    // Ensure the element can receive focus
    if (!htmlElement.getAttribute('tabindex')) {
      htmlElement.setAttribute('tabindex', '-1');
    }
    
    // Add aria-label if missing
    if (!htmlElement.getAttribute('aria-label')) {
      htmlElement.setAttribute('aria-label', 'Main content');
    }
    
    // Ensure proper role
    if (!htmlElement.getAttribute('role') && htmlElement.tagName.toLowerCase() !== 'main') {
      htmlElement.setAttribute('role', 'main');
    }
  });
  
  // Add event listener for skip link activation
  document.addEventListener('click', handleSkipLinkClick);
  document.addEventListener('keydown', handleSkipLinkKeydown);
}

/**
 * Handle skip link clicks
 */
function handleSkipLinkClick(event: Event) {
  const target = event.target as HTMLElement;
  
  if (target.classList.contains('skip-links')) {
    const href = target.getAttribute('href');
    if (href && href.startsWith('#')) {
      const targetElement = document.querySelector(href) as HTMLElement;
      if (targetElement) {
        // Ensure smooth scrolling and proper focus
        setTimeout(() => {
          targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
          targetElement.focus();
        }, 100);
      }
    }
  }
}

/**
 * Handle skip link keyboard navigation
 */
function handleSkipLinkKeydown(event: KeyboardEvent) {
  const target = event.target as HTMLElement;
  
  if (target.classList.contains('skip-links') && (event.key === 'Enter' || event.key === ' ')) {
    event.preventDefault();
    target.click();
  }
}

/**
 * Cleanup skip link event listeners
 */
export function cleanupSkipLinks() {
  document.removeEventListener('click', handleSkipLinkClick);
  document.removeEventListener('keydown', handleSkipLinkKeydown);
}

/**
 * Auto-initialize when DOM is ready
 */
if (typeof window !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeSkipLinks);
  } else {
    initializeSkipLinks();
  }
}
