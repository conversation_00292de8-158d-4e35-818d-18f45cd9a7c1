import { createContext, useContext, useState } from "react";

type ProgressProviderProps = {
  children: React.ReactNode;
  progress: number;
};

type ProgressProviderState = {
  progress: number;
  setProgress: (progress: number) => void;
};

const initialState: ProgressProviderState = {
  progress: 0,
  setProgress: () => null,
};

const ProgressProviderContext =
  createContext<ProgressProviderState>(initialState);

export function ProgressProvider({
  children,
  progress,
  ...props
}: ProgressProviderProps) {
  const [progressPercentage, setProgress] = useState<number>(progress);

  const value = {
    progress: progressPercentage,
    setProgress: (progress: number) => {
      setProgress(progress);
    },
  };

  return (
    <ProgressProviderContext.Provider {...props} value={value}>
      {children}
    </ProgressProviderContext.Provider>
  );
}

export const useProgress = () => {
  const context = useContext(ProgressProviderContext);

  if (context === undefined)
    throw new Error("useTheme must be used within a ProgressProvider");

  return context;
};
