/**
 * Location detection utilities for automatic country/region selection
 */

import { getCountryCallingCode, getCountries } from 'libphonenumber-js';

export interface LocationResult {
  countryCode: string;
  success: boolean;
  method: 'geolocation' | 'ip' | 'fallback';
}

/**
 * Cache for the dynamic country code mapping
 */
let countryCodeMappingCache: Record<string, string> | null = null;

/**
 * Dynamically generate country code mapping from libphonenumber-js
 * This ensures we have complete coverage of all supported countries
 */
function getCountryCodeMapping(): Record<string, string> {
  // Return cached mapping if available
  if (countryCodeMappingCache) {
    return countryCodeMappingCache;
  }

  // Generate mapping from all supported countries in libphonenumber-js
  const mapping: Record<string, string> = {};

  try {
    const countries = getCountries();

    countries.forEach(countryCode => {
      // Convert to uppercase for geolocation API compatibility
      // and lowercase for libphonenumber-js compatibility
      mapping[countryCode.toUpperCase()] = countryCode.toLowerCase();
    });

    // Cache the result
    countryCodeMappingCache = mapping;

    console.log(`Generated dynamic country mapping for ${countries.length} countries`);
    return mapping;
  } catch (error) {
    console.warn('Failed to generate dynamic country mapping, using fallback:', error);

    // Fallback to essential countries if dynamic generation fails
    const fallbackMapping = {
      'US': 'us',
      'CA': 'ca',
      'GB': 'gb',
      'DE': 'de',
      'FR': 'fr',
      'IT': 'it',
      'ES': 'es',
      'AU': 'au',
      'JP': 'jp',
      'CN': 'cn',
      'IN': 'in',
      'BR': 'br',
      'MX': 'mx',
      'RU': 'ru',
      'ZA': 'za',
    };

    countryCodeMappingCache = fallbackMapping;
    return fallbackMapping;
  }
}

/**
 * Detect user's country using browser geolocation API
 */
async function detectCountryByGeolocation(): Promise<LocationResult> {
  return new Promise((resolve) => {
    if (!navigator.geolocation) {
      resolve({
        countryCode: 'us',
        success: false,
        method: 'fallback'
      });
      return;
    }

    const timeoutId = setTimeout(() => {
      resolve({
        countryCode: 'us',
        success: false,
        method: 'fallback'
      });
    }, 5000); // 5 second timeout

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        clearTimeout(timeoutId);
        try {
          // Use reverse geocoding to get country from coordinates
          const { latitude, longitude } = position.coords;
          const countryCode = await reverseGeocode(latitude, longitude);
          
          const mapping = getCountryCodeMapping();
          resolve({
            countryCode: mapping[countryCode.toUpperCase()] || 'us',
            success: true,
            method: 'geolocation'
          });
        } catch (error) {
          console.warn('Reverse geocoding failed:', error);
          resolve({
            countryCode: 'us',
            success: false,
            method: 'fallback'
          });
        }
      },
      (error) => {
        clearTimeout(timeoutId);
        console.warn('Geolocation failed:', error);
        resolve({
          countryCode: 'us',
          success: false,
          method: 'fallback'
        });
      },
      {
        timeout: 5000,
        enableHighAccuracy: false,
        maximumAge: 300000 // 5 minutes cache
      }
    );
  });
}

/**
 * Reverse geocode coordinates to get country code
 */
async function reverseGeocode(lat: number, lng: number): Promise<string> {
  try {
    // Using a free geocoding service (you might want to replace with a more reliable one)
    const response = await fetch(
      `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${lat}&longitude=${lng}&localityLanguage=en`
    );
    
    if (!response.ok) {
      throw new Error('Geocoding service unavailable');
    }
    
    const data = await response.json();
    return data.countryCode || 'US';
  } catch (error) {
    console.warn('Reverse geocoding service failed:', error);
    throw error;
  }
}

/**
 * Detect user's country using IP-based location detection
 */
async function detectCountryByIP(): Promise<LocationResult> {
  try {
    // Using a free IP geolocation service
    const response = await fetch('https://ipapi.co/json/', {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('IP geolocation service unavailable');
    }

    const data = await response.json();
    const countryCode = data.country_code;

    if (countryCode) {
      const mapping = getCountryCodeMapping();
      return {
        countryCode: mapping[countryCode.toUpperCase()] || 'us',
        success: true,
        method: 'ip'
      };
    }

    throw new Error('No country code in response');
  } catch (error) {
    console.warn('IP-based location detection failed:', error);
    return {
      countryCode: 'us',
      success: false,
      method: 'fallback'
    };
  }
}

/**
 * Main function to detect user's location with fallback strategy
 */
export async function detectUserLocation(): Promise<LocationResult> {


  // Fallback to IP-based detection
  const ipResult = await detectCountryByIP();
  if (ipResult.success) {
    return ipResult;
  }

  // Final fallback to US
  return {
    countryCode: 'us',
    success: false,
    method: 'fallback'
  };
}

/**
 * Validate if a country code is supported by libphonenumber-js
 */
export function isValidCountryCode(countryCode: string): boolean {
  try {
    getCountryCallingCode(countryCode.toUpperCase() as any);
    return true;
  } catch {
    return false;
  }
}

/**
 * Get a safe country code that's guaranteed to work with libphonenumber-js
 */
export function getSafeCountryCode(countryCode: string): string {
  const normalizedCode = countryCode.toLowerCase();

  if (isValidCountryCode(normalizedCode)) {
    return normalizedCode;
  }

  // Fallback to US if the detected country code is not supported
  return 'us';
}

/**
 * Get all supported countries from libphonenumber-js
 * @returns Array of country codes in lowercase format
 */
export function getSupportedCountries(): string[] {
  try {
    return getCountries().map(code => code.toLowerCase());
  } catch (error) {
    return ['us', 'ca', 'gb', 'de', 'fr', 'it', 'es', 'au', 'jp'];
  }
}

/**
 * Get the current country code mapping
 * @returns Record of uppercase country codes to lowercase country codes
 */
export function getCurrentCountryMapping(): Record<string, string> {
  return getCountryCodeMapping();
}

/**
 * Clear the country code mapping cache
 * Useful for testing or if you want to regenerate the mapping
 */
export function clearCountryMappingCache(): void {
  countryCodeMappingCache = null;
}

/**
 * Get statistics about the current country mapping
 */
export function getCountryMappingStats(): {
  totalCountries: number;
  sampleCountries: string[];
  cacheStatus: 'cached' | 'not-cached';
} {
  const mapping = getCountryCodeMapping();
  const countries = Object.keys(mapping);

  return {
    totalCountries: countries.length,
    sampleCountries: countries.slice(0, 10), // First 10 countries as sample
    cacheStatus: countryCodeMappingCache ? 'cached' : 'not-cached'
  };
}
