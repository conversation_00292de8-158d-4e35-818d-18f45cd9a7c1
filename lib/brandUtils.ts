/**
 * Utility functions for brand-specific logic
 */

/**
 * Brands that support dynamic font sizing
 * UE brands (University Europe) include UEG and other UE-related brands
 */
const DYNAMIC_FONT_SIZE_BRANDS = ["ue", "ueg"];

/**
 * Check if the current brand supports dynamic font sizing
 * @returns boolean indicating if dynamic font sizing should be applied
 */
export const shouldApplyDynamicFontSize = (): boolean => {
  const currentBrand = process.env.NEXT_PUBLIC_OAP_NAME?.toLowerCase();
  return DYNAMIC_FONT_SIZE_BRANDS.includes(currentBrand || "");
};

/**
 * Get the appropriate style object for font sizing based on brand
 * @param fontSize - The fontSize object from the atom
 * @param fontSizeKey - The key to look up in the fontSize object
 * @returns Style object with fontSize if applicable, empty object otherwise
 */
export const getBrandSpecificFontStyle = (
  fontSize: any,
  fontSizeKey: string
): React.CSSProperties => {
  if (shouldApplyDynamicFontSize() && fontSize[fontSizeKey]) {
    return { fontSize: fontSize[fontSizeKey] };
  }
  return {};
};