/**
 * Utility functions for preventing caching of sensitive content
 * Addresses CWE-524: Storable and Cacheable Content vulnerability
 */

/**
 * Get cache prevention headers for API responses
 */
export const getCachePreventionHeaders = () => ({
  "Cache-Control": "no-cache, no-store, must-revalidate, private, max-age=0",
  Pragma: "no-cache",
  Expires: "0",
  "X-Content-Type-Options": "nosniff",
  Vary: "Authorization, Accept-Encoding, User-Agent",
  "Last-Modified": new Date(0).toUTCString(), // Set to epoch to prevent caching
});

/**
 * Get fetch options with cache prevention
 */
export const getCachePreventionFetchOptions = (
  options: RequestInit = {}
): RequestInit => ({
  ...options,
  cache: "no-store",
  credentials: "same-origin",
  headers: {
    ...getCachePreventionHeaders(),
    ...options.headers,
  },
});

/**
 * Check if content contains sensitive information that should not be cached
 */
export const isSensitiveContent = (data: any): boolean => {
  if (!data || typeof data !== "object") return false;

  const sensitiveFields = [
    "email",
    "phone",
    "ssn",
    "passport",
    "applicationId",
    "studentId",
    "personalInfo",
    "address",
    "dateOfBirth",
    "financialInfo",
    "documents",
  ];

  const dataString = JSON.stringify(data).toLowerCase();
  return sensitiveFields.some((field) =>
    dataString.includes(field.toLowerCase())
  );
};

/**
 * Add meta tags to prevent page caching in browser history
 */
export const addNoCacheMetaTags = () => {
  if (typeof window === "undefined") return;

  // Remove existing cache meta tags
  const existingTags = document.querySelectorAll(
    'meta[http-equiv="Cache-Control"], meta[http-equiv="Pragma"], meta[http-equiv="Expires"], meta[name="cache-control"], meta[name="pragma"], meta[name="expires"]'
  );
  existingTags.forEach((tag) => tag.remove());

  // Add http-equiv meta tags (more effective for cache prevention)
  const httpEquivTags = [
    {
      httpEquiv: "Cache-Control",
      content: "no-cache, no-store, must-revalidate, private, max-age=0",
    },
    { httpEquiv: "Pragma", content: "no-cache" },
    { httpEquiv: "Expires", content: "0" },
  ];

  httpEquivTags.forEach((tagData) => {
    const meta = document.createElement("meta");
    meta.httpEquiv = tagData.httpEquiv;
    meta.content = tagData.content;
    document.head.appendChild(meta);
  });
};

/**
 * Clear sensitive data from browser storage
 */
export const clearSensitiveStorage = () => {
  if (typeof window === "undefined") return;

  try {
    // Clear session storage completely
    sessionStorage.clear();

    // Clear specific localStorage items that might contain sensitive data
    const sensitiveKeys = [
      "email",
      "applicationId",
      "studentDetails",
      "formData",
      "accessToken",
      "idToken",
      "refreshToken",
      "userSession",
      "authTokens",
      "personalInfo",
      "applicationData",
    ];

    sensitiveKeys.forEach((key) => {
      localStorage.removeItem(key);
    });

    // Clear any keys that might contain sensitive patterns
    const sensitivePatterns = [
      "token",
      "auth",
      "session",
      "user",
      "student",
      "application",
    ];
    Object.keys(localStorage).forEach((key) => {
      if (
        sensitivePatterns.some((pattern) => key.toLowerCase().includes(pattern))
      ) {
        localStorage.removeItem(key);
      }
    });
  } catch (error) {
    console.warn("Failed to clear sensitive storage:", error);
  }
};
