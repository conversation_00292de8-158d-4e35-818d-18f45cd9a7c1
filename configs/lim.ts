const redirectSignIn =
  typeof window !== "undefined"
    ? `${window.location.origin}/application`
    : "http://localhost:3000/application";
const redirectSignOut =
  typeof window !== "undefined"
    ? `${window.location.origin}/login`
    : "http://localhost:3000/application";

export const limAwsConfig = {
  aws_project_region: "eu-west-1",
  aws_appsync_authenticationType: "AMAZON_COGNITO_USER_POOLS",
  aws_cognito_identity_pool_id:
    "eu-west-1:f19ebae5-d449-44bc-9691-73daf23ad9cf",
  aws_cognito_region: "eu-west-1",
  aws_user_pools_id: "eu-west-1_w5fuKTnEJ",
  aws_user_pools_web_client_id: "2qjqh02ucokk8pvg9g9de5c75n",
  oauth: {
    domain: "lim-student-oap-dev.auth.eu-west-1.amazoncognito.com",
    redirectSignIn,
    redirectSignOut,
    responseType: "id_token",
    scope: [
      "email",
      "openid",
      "phone",
      "profile",
      "aws.cognito.signin.user.admin",
    ],
    socialProviders: ["LinkedIn"],
  },
};
