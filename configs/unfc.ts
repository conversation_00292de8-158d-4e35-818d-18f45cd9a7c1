const redirectSignIn =
  typeof window !== "undefined"
    ? `${window.location.origin}/application`
    : "http://localhost:3000/application";
const redirectSignOut =
  typeof window !== "undefined"
    ? `${window.location.origin}/login`
    : "http://localhost:3000/application";

export const unfcAwsConfig = {
  aws_project_region: "eu-west-1",
  aws_appsync_authenticationType: "AMAZON_COGNITO_USER_POOLS",
  aws_cognito_identity_pool_id:
    "eu-west-1:e04a5803-ead2-4eaa-9360-6f1c7291f19c",
  aws_cognito_region: "eu-west-1",
  aws_user_pools_id: "eu-west-1_4yORyxiwf",
  aws_user_pools_web_client_id: "3qv53jimv05is5nl8rd9c8oh",
  oauth: {
    domain: "unfc-student-oap-dev.auth.eu-west-1.amazoncognito.com",
    redirectSignIn,
    redirectSignOut,
    responseType: "id_token",
    scope: [
      "email",
      "openid",
      "phone",
      "profile",
      "aws.cognito.signin.user.admin",
    ],
  },
};
