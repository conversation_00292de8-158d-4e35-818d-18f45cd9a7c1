const redirectSignIn =
  typeof window !== "undefined"
    ? `${window.location.origin}/application`
    : "http://localhost:3000/application";
const redirectSignOut =
  typeof window !== "undefined"
    ? `${window.location.origin}/login`
    : "http://localhost:3000/application";

export const hzuAwsConfig = {
  aws_project_region: "eu-west-1",
  aws_appsync_authenticationType: "AMAZON_COGNITO_USER_POOLS",
  aws_cognito_identity_pool_id:
    "eu-west-1:94618dc6-4b70-404d-b1d9-a297cb0ea0ac",
  aws_cognito_region: "eu-west-1",
  aws_user_pools_id: "eu-west-1_4zuOP58FI",
  aws_user_pools_web_client_id: "4j950h9dl8skm5gfg36thh5u6c",
  oauth: {
    domain: "hzu-student-oap-dev.auth.eu-west-1.amazoncognito.com",
    redirectSignIn,
    redirectSignOut,
    responseType: "id_token",
    scope: [
      "email",
      "openid",
      "phone",
      "profile",
      "aws.cognito.signin.user.admin",
    ],
  },
};
