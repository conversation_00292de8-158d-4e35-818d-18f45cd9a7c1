const redirectSignIn =
  typeof window !== "undefined"
    ? `${window.location.origin}/application`
    : "http://localhost:3000/application";
const redirectSignOut =
  typeof window !== "undefined"
    ? `${window.location.origin}/login`
    : "http://localhost:3000/application";

const devUCWAwsConfig = {
  aws_project_region: "eu-west-1",
  aws_appsync_authenticationType: "AMAZON_COGNITO_USER_POOLS",
  aws_cognito_identity_pool_id:
    "eu-west-1:0c870285-d7c7-4f78-986f-ce326cc04ebe",
  aws_cognito_region: "eu-west-1",
  aws_user_pools_id: "eu-west-1_isJtz7XfY",
  aws_user_pools_web_client_id: "mi02thfgb5ql794ejc72c0ae8",
  oauth: {
    domain: "ucw-student-oap-dev.auth.eu-west-1.amazoncognito.com",
    redirectSignIn,
    redirectSignOut,
    responseType: "id_token",
    scope: [
      "email",
      "openid",
      "phone",
      "profile",
      "aws.cognito.signin.user.admin",
    ],
  },
};

const prodUCWAwsConfig = {
  aws_project_region: "eu-west-1",
  aws_appsync_authenticationType: "AMAZON_COGNITO_USER_POOLS",
  aws_cognito_identity_pool_id:
    "eu-west-1:85ecda47-8068-445d-8088-7570b44a0e98",
  aws_cognito_region: "eu-west-1",
  aws_user_pools_id: "eu-west-1_3PvBCuLRl",
  aws_user_pools_web_client_id: "5mt1kt84vs1ag22ksndta6g0lg",
  oauth: {
    domain: "ucw-student-oap-prod.auth.eu-west-1.amazoncognito.com",
    redirectSignIn,
    redirectSignOut,
    responseType: "id_token",
    scope: [
      "email",
      "openid",
      "phone",
      "profile",
      "aws.cognito.signin.user.admin",
    ],
  },
};

export const ucwAwsConfig =
  process.env.NEXT_PUBLIC_NODE_ENV === "prod"
    ? prodUCWAwsConfig
    : devUCWAwsConfig;
