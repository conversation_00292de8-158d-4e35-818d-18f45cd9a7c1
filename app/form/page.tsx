/* eslint-disable @next/next/no-async-client-component */
"use client";
import SectionalForm from "@/components/Forms/sectionFrom";
import { fetchAuthSession } from "aws-amplify/auth";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function Page() {
  const router = useRouter();

  useEffect(() => {
    const checkUser = async () => {
      try {
        await fetchAuthSession();
      } catch {
        router.push("/login"); // Redirect to login page if not authenticated
      }
    };
    checkUser();
  }, [router]);

  return (
    <div className="flex w-full bg-background h-screen">
      <SectionalForm />
    </div>
  );
}
