"use client";
import {
  getAccessToken,
  getLookUpData,
  getOapForm,
  getOapFormSections,
} from "@/api/api";
import PrequalifyingQuestionnairePopup from "@/components/custom/pre-qualifying-questionnaire-popup";
import {
  applicationId,
  consumerAPIKey,
  email,
  nextForm,
  preferredLanguage,
} from "@/lib/atom";
import { useQuery } from "@tanstack/react-query";
import { useAtom } from "jotai";
import { useEffect, useState } from "react";
import loader from "../../public/loader.svg";
import Image from "next/image";

export default function Page() {
  const [showPrequalifyingModal, setShowPrequalifyingModal] =
    useState<boolean>(true);
  const [applicationFields, setApplicationFields] = useState<any>({});
  const [application, setApplication] = useAtom(applicationId);
  const [apiKey] = useAtom(consumerAPIKey);
  const [nextFormDetails, setNextFormDetails]: any = useAtom(nextForm);
  const [preferredLang] = useAtom(preferredLanguage);

  const { data: sectionQuery, isFetching: sectionIsFetching } = useQuery({
    queryKey: [
      `${nextFormDetails?.oap}-${nextFormDetails?.mode}-${nextFormDetails?.form}`,
    ],
    queryFn: async () => {
      if (
        nextFormDetails?.form &&
        nextFormDetails?.oap &&
        nextFormDetails?.mode
      ) {
        let res = await getOapForm(
          {
            oap: nextFormDetails?.oap,
            form: nextFormDetails?.form,
            mode: nextFormDetails?.mode,
          },
          apiKey,
          await getAccessToken()
        );
        return res;
      } else {
        return;
      }
    },
    enabled: !!nextFormDetails,
  });

  const {
    data: questionariesSectionQuery,
    isFetching: questionariesSectionQueryFetching,
  } = useQuery({
    queryKey: [`questionnaire-section`, application],
    queryFn: async () => {
      let res = await getOapFormSections(
        {
          oap: process.env.NEXT_PUBLIC_OAP_NAME,
          mode: process.env.NEXT_PUBLIC_OAP_MODE,
          formName: "PROGRAM_FILTER",
          sectionName: "QUESTIONNAIRE_POPUP",
        },
        apiKey
      );
      return res;
    },
    // enabled:
    //   !!apiKey
    //   &&
    //   formQuery?.fieldData?.filter((item: any) => item.type == "button")[0]
    //     ?.canHaveQuestionnarieFields,
  });

  const { data: labels, isFetching: labelFetching } = useQuery({
    queryKey: [`oap/lookup/programmes`],
    queryFn: async () => {
      let res = await getLookUpData(
        {
          name: "oap/lookup/programmes",
        },
        apiKey,
        await getAccessToken(),
        { ...(preferredLang && { displayLanguage: preferredLang }) }
      );
      return res;
    },
    enabled: !!apiKey,
  });

  if (questionariesSectionQueryFetching || sectionIsFetching || labelFetching) {
    return (
      <main className="min-h-screen bg-on-background flex flex-col items-center justify-center overflow-scroll w-full">
        <Image priority src={loader} height={32} width={32} alt="Loading..." />
      </main>
    );
  }

  return (
    <div>
      <PrequalifyingQuestionnairePopup
        isOpen={showPrequalifyingModal}
        setShowPrequalifyingModal={setShowPrequalifyingModal}
        fieldData={questionariesSectionQuery?.fieldData}
        questionariesSectionQuery={questionariesSectionQuery}
        basicDetails={applicationFields}
        sectionQuery={sectionQuery}
        labels={labels}
      />
    </div>
  );
}
