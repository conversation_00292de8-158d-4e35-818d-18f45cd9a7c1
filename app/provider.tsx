"use client";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { ProgressProvider } from "@/lib/progress-context";
import { FormProvider, useForm } from "react-hook-form";
import { Amplify } from "aws-amplify";
import { getAwsConfiguration } from "@/helpers/getAwsConfig";

export default function Providers({ children }: any) {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        refetchOnWindowFocus: false,
      },
    },
  });
  const methods = useForm();

  Amplify.configure(getAwsConfiguration());

  return (
    <QueryClientProvider client={queryClient}>
      <ProgressProvider progress={0}>
        <FormProvider {...methods}>{children}</FormProvider>
      </ProgressProvider>
    </QueryClientProvider>
  );
}
