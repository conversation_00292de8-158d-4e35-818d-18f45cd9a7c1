"use client";

import Login from "@/components/Forms/loginForm";
import { email, qualifyingQuestions } from "@/lib/atom";
import {
  fetchAuthSession,
  fetchUserAttributes,
  signOut,
} from "aws-amplify/auth";
import { useAtom } from "jotai";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

export default function Page() {
  const router = useRouter();

  const searchParams = useSearchParams();

  const confirmationCode: any = searchParams?.get("confirmation_code");
  const userEmail: any = searchParams?.get("email");

  const utmSource = searchParams?.get("utm_source");
  const utmMedium = searchParams?.get("utm_medium");
  const utmCampaign = searchParams?.get("utm_campaign");
  const utmContent = searchParams?.get("utm_content");
  const utmTerm = searchParams?.get("utm_term");
  const utmNetwork = searchParams?.get("utm_network");
  const utmReferrer = searchParams?.get("utm_referrer");

  useEffect(() => {
    if (typeof window !== "undefined") {
      const utmParams: Record<string, string> = {};

      if (utmSource) utmParams.utmSource = utmSource;
      if (utmMedium) utmParams.utmMedium = utmMedium;
      if (utmCampaign) utmParams.utmCampaign = utmCampaign;
      if (utmContent) utmParams.utmContent = utmContent;
      if (utmTerm) utmParams.utmTerm = utmTerm;
      if (utmNetwork) utmParams.utmNetwork = utmNetwork;
      if (utmReferrer) utmParams.utmReferrer = utmReferrer;

      // Only store if there are any UTM parameters
      if (Object.keys(utmParams).length > 0) {
        console.log("setting utm params", utmParams);
        sessionStorage.setItem("utmParams", JSON.stringify(utmParams));
      }
    }
  }, []);

  useEffect(() => {
    const checkUser = async () => {
      try {
        const { tokens } = await fetchAuthSession();
        const userAttributes = tokens?.idToken?.payload;

        if (tokens?.accessToken.toString()) {
          if (!userEmail && !confirmationCode) {
            router.push("/application-filter");
          } else if (userEmail && confirmationCode) {
            if (userAttributes?.email === userEmail) {
              router.push("/application-filter");
            } else {
              await signOut();
              router.push("/login");
            }
          }
        } else {
          router.push("/login");
        }
      } catch {
        router.push("/login"); // Redirect to login page if not authenticated
      }
    };
    checkUser();
  }, []);

  return <Login />;
}
