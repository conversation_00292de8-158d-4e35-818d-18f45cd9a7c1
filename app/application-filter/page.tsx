"use client";
import { fetchAuthSession, getCurrentUser } from "aws-amplify/auth";
import ApplicationFilter from "@/components/Forms/applicationFilter";
import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAtom } from "jotai";
import { nextForm } from "@/lib/atom";
import UegApplicationFilter from "@/components/Forms/custom-filter/uegApplicationFilter";

export default function Page() {
  const router = useRouter();
  const [, setNextFormDetails] = useAtom(nextForm);

  useEffect(() => {
    const checkUser = async () => {
      try {
        await fetchAuthSession();
        setNextFormDetails({
          form: "PROGRAM_FILTER",
          mode: process.env.NEXT_PUBLIC_OAP_MODE,
          oap: process.env.NEXT_PUBLIC_OAP_NAME,
          type: "single",
        });
      } catch {
        router.push("/login");
      }
    };

    checkUser();
  }, []);

  const renderApplicationFilter = () => {
    switch (process.env.NEXT_PUBLIC_OAP_NAME) {
      case 'UEG':
        return <UegApplicationFilter />;
      default:
        return <ApplicationFilter />;
    }
  }

  return (
    <div className="flex w-full bg-background h-screen">
      {renderApplicationFilter()}
    </div>
  );
}
