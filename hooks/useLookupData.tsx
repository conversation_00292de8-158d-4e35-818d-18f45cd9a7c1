import { getLookUpData } from "@/api/api";
import { consumerAPIKey } from "@/lib/atom";
import { useQueryClient } from "@tanstack/react-query";
import { useAtom } from "jotai";
interface lookupDataProps {
  queryKey?: string;
  name?: string;
  displayLanguage?: string;
}

export const useLookUpData = () => {
  const queryClient = useQueryClient();
  const [apiKey] = useAtom(consumerAPIKey);

  const fetchLookUpData = async ({
    queryKey,
    name,
    displayLanguage,
  }: lookupDataProps) => {
    const result = await getLookUpData({ name: name }, apiKey, "", {
      ...(displayLanguage && { displayLanguage: displayLanguage }),
    });
    queryClient.setQueryData([queryKey], result);
    return result;
  };

  return { data: fetchLookUpData };
};
