import { RECAPTCHA_SITE_KEY } from "@/configs/analytics";
import { recaptchaEnabledAtom } from "@/lib/atom";
import { useAtom } from "jotai";
import { useCallback, useEffect, useState } from "react";

declare global {
  interface Window {
    grecaptcha: {
      ready: (callback: () => void) => void;
      execute: (
        siteKey: string,
        options: { action: string }
      ) => Promise<string>;
    };
  }
}

interface UseRecaptchaReturn {
  executeRecaptcha: (action: string) => Promise<string | null>;
  isRecaptchaLoaded: boolean;
  recaptchaError: string | null;
}

export const useRecaptcha = (): UseRecaptchaReturn => {
  const [isRecaptchaLoaded, setIsRecaptchaLoaded] = useState(false);
  const [recaptchaError, setRecaptchaError] = useState<string | null>(null);
  const [isRecaptchaEnabled] = useAtom(recaptchaEnabled<PERSON>tom);

  const brand = process.env.NEXT_PUBLIC_OAP_NAME?.toUpperCase();
  const env = process.env.NEXT_PUBLIC_NODE_ENV?.toUpperCase() === "PROD" ? "PROD" : "DEV";
  const siteKey = RECAPTCHA_SITE_KEY[`${brand}_${env}`];

  
  useEffect(() => {
    if (!isRecaptchaEnabled) {
      return;
    }

    if (!siteKey) {
      setRecaptchaError("reCAPTCHA site key is not configured for this brand/environment");
      return;
    }

    // Check if reCAPTCHA is already loaded
    if (window.grecaptcha) {
      setIsRecaptchaLoaded(true);
      return;
    }

    // Load reCAPTCHA script if not already loaded
    const script = document.createElement("script");
    script.src = `https://www.google.com/recaptcha/api.js?render=${siteKey}`;
    script.async = true;
    script.defer = true;

    script.onload = () => {
      if (window.grecaptcha) {
        window.grecaptcha.ready(() => {
          setIsRecaptchaLoaded(true);
          setRecaptchaError(null);
        });
      }
    };

    script.onerror = () => {
      setRecaptchaError("Failed to load reCAPTCHA script");
    };

    document.head.appendChild(script);

    return () => {
      // Clean up script if component unmounts
      try {
        const existingScript = document.querySelector(
          `script[src*="recaptcha"]`
        );
        if (existingScript && existingScript.parentNode) {
          existingScript.parentNode.removeChild(existingScript);
        }
      } catch (error) {
        // Silently handle cleanup errors as the script might have already been removed
        console.warn("reCAPTCHA script cleanup warning:", error);
      }
    };
  }, [siteKey, isRecaptchaEnabled]);

  const executeRecaptcha = useCallback(
    async (action: string): Promise<string | null> => {
      if (!siteKey) {
        setRecaptchaError("reCAPTCHA site key is not configured");
        return null;
      }

      if (!isRecaptchaLoaded || !window.grecaptcha) {
        setRecaptchaError("reCAPTCHA is not loaded yet");
        return null;
      }

      try {
        const token = await window.grecaptcha.execute(siteKey, { action });
        setRecaptchaError(null);
        return token;
      } catch (error) {
        console.error("reCAPTCHA execution failed:", error);
        setRecaptchaError("Failed to execute reCAPTCHA");
        return null;
      }
    },
    [siteKey, isRecaptchaLoaded]
  );

  return {
    executeRecaptcha,
    isRecaptchaLoaded,
    recaptchaError,
  };
};
