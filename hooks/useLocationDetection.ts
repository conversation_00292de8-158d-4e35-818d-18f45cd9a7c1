/**
 * Custom hook for detecting user location and auto-selecting country code
 */

import { useState, useEffect, useCallback } from 'react';
import { detectUserLocation, getSafeCountryCode, type LocationResult } from '@/lib/locationUtils';

export interface UseLocationDetectionOptions {
  /** Whether to automatically detect location on mount */
  autoDetect?: boolean;
  /** Default country code to use if detection fails */
  defaultCountry?: string;
  /** Whether to cache the result in localStorage */
  enableCache?: boolean;
  /** Cache expiry time in milliseconds (default: 24 hours) */
  cacheExpiry?: number;
}

export interface UseLocationDetectionResult {
  /** Detected country code */
  countryCode: string;
  /** Whether location detection is in progress */
  isDetecting: boolean;
  /** Whether detection was successful */
  isSuccess: boolean;
  /** Detection method used */
  method: LocationResult['method'] | null;
  /** Error message if detection failed */
  error: string | null;
  /** Manually trigger location detection */
  detectLocation: () => Promise<void>;
  /** Reset to default country */
  resetToDefault: () => void;
}

const CACHE_KEY = 'mobile-input-country-detection';

interface CachedLocationResult {
  countryCode: string;
  method: LocationResult['method'];
  timestamp: number;
}

export function useLocationDetection(options: UseLocationDetectionOptions = {}): UseLocationDetectionResult {
  const {
    autoDetect = true,
    defaultCountry = 'us',
    enableCache = true,
    cacheExpiry = 24 * 60 * 60 * 1000, // 24 hours
  } = options;

  const [countryCode, setCountryCode] = useState<string>(defaultCountry);
  const [isDetecting, setIsDetecting] = useState<boolean>(false);
  const [isSuccess, setIsSuccess] = useState<boolean>(false);
  const [method, setMethod] = useState<LocationResult['method'] | null>(null);
  const [error, setError] = useState<string | null>(null);

  /**
   * Get cached location result if available and not expired
   */
  const getCachedResult = useCallback((): CachedLocationResult | null => {
    if (!enableCache || typeof window === 'undefined') {
      return null;
    }

    try {
      const cached = localStorage.getItem(CACHE_KEY);
      if (!cached) return null;

      const parsedCache: CachedLocationResult = JSON.parse(cached);
      const isExpired = Date.now() - parsedCache.timestamp > cacheExpiry;

      if (isExpired) {
        localStorage.removeItem(CACHE_KEY);
        return null;
      }

      return parsedCache;
    } catch (error) {
      console.warn('Failed to read location cache:', error);
      localStorage.removeItem(CACHE_KEY);
      return null;
    }
  }, [enableCache, cacheExpiry]);

  /**
   * Cache location result
   */
  const setCachedResult = useCallback((result: LocationResult): void => {
    if (!enableCache || typeof window === 'undefined' || !result.success) {
      return;
    }

    try {
      const cacheData: CachedLocationResult = {
        countryCode: result.countryCode,
        method: result.method,
        timestamp: Date.now(),
      };
      localStorage.setItem(CACHE_KEY, JSON.stringify(cacheData));
    } catch (error) {
      console.warn('Failed to cache location result:', error);
    }
  }, [enableCache]);

  /**
   * Perform location detection
   */
  const detectLocation = useCallback(async (): Promise<void> => {

    // Check cache first
    const cachedResult = getCachedResult();
    if (cachedResult) {
      setCountryCode(getSafeCountryCode(cachedResult.countryCode));
      setIsSuccess(true);
      setMethod(cachedResult.method);
      setError(null);
      return;
    }
    setIsDetecting(true);
    setError(null);

    try {
      const result = await detectUserLocation();
      const safeCountryCode = getSafeCountryCode(result.countryCode);

      setCountryCode(safeCountryCode);
      setIsSuccess(result.success);
      setMethod(result.method);

      if (result.success) {
        setCachedResult(result);
      } else {
        setError('Location detection failed, using default country');
      }
    } catch (err) {
      setCountryCode(getSafeCountryCode(defaultCountry));
      setIsSuccess(false);
      setMethod('fallback');
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsDetecting(false);
    }
  }, [getCachedResult, setCachedResult, defaultCountry]);

  /**
   * Reset to default country
   */
  const resetToDefault = useCallback((): void => {
    setCountryCode(getSafeCountryCode(defaultCountry));
    setIsSuccess(false);
    setMethod('fallback');
    setError(null);
    
    // Clear cache
    if (enableCache && typeof window !== 'undefined') {
      localStorage.removeItem(CACHE_KEY);
    }
  }, [defaultCountry, enableCache]);

  /**
   * Auto-detect location on mount
   */
  useEffect(() => {
    if (autoDetect) {
      detectLocation().catch(err => {
      });
    }
  }, [autoDetect, detectLocation]);

  return {
    countryCode,
    isDetecting,
    isSuccess,
    method,
    error,
    detectLocation,
    resetToDefault,
  };
}
