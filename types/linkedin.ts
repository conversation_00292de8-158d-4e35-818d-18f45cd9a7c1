// LinkedIn OAuth and user data types

export interface LinkedInUserProfile {
  id: string;
  firstName: {
    localized: {
      [locale: string]: string;
    };
    preferredLocale: {
      country: string;
      language: string;
    };
  };
  lastName: {
    localized: {
      [locale: string]: string;
    };
    preferredLocale: {
      country: string;
      language: string;
    };
  };
  profilePicture?: {
    displayImage: string;
  };
}

export interface LinkedInEmailResponse {
  elements: Array<{
    "handle~": {
      emailAddress: string;
    };
    handle: string;
    primary: boolean;
    type: string;
  }>;
}

export interface LinkedInAuthResponse {
  access_token: string;
  expires_in: number;
  scope: string;
  token_type: string;
  refresh_token?: string;
}

export interface LinkedInUserData {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  fullName: string;
  profilePicture?: string;
  locale?: string;
}

export interface LinkedInAuthError {
  error: string;
  error_description: string;
  error_uri?: string;
}

export interface LinkedInOAuthConfig {
  clientId: string;
  redirectUri: string;
  scope: string[];
  state?: string;
}

// AWS Cognito federated sign-in response types
export interface CognitoFederatedUser {
  username: string;
  attributes: {
    email: string;
    email_verified: string;
    family_name?: string;
    given_name?: string;
    name?: string;
    picture?: string;
    sub: string;
    identities?: string;
  };
}

export interface AuthenticationResult {
  isSignedIn: boolean;
  user?: CognitoFederatedUser;
  error?: string;
}
