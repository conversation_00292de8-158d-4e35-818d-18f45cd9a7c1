/**
 * Type definitions for OAuth Loader Modal Component
 */

export type OAuthProvider = "linkedin" | "google" | "facebook" | "github" | "generic";

export interface OAuthProviderConfig {
  icon: string;
  defaultMessage: string;
  defaultSubMessage: string;
  primaryColor: string;
  secondaryColor: string;
}

export interface OAuthLoaderModalProps {
  /** Controls whether the modal is visible */
  isOpen: boolean;
  
  /** OAuth provider type - determines branding and default messages */
  provider?: OAuthProvider;
  
  /** Custom main message to display */
  message?: string;
  
  /** Custom sub message to display */
  subMessage?: string;
  
  /** Callback function when cancel button is clicked */
  onCancel?: () => void;
  
  /** Whether to show the cancel button */
  showCancelButton?: boolean;
}

export interface OAuthLoaderState {
  isLoading: boolean;
  provider: OAuthProvider;
  error?: string;
}

/**
 * OAuth authentication result types
 */
export interface OAuthAuthenticationResult {
  isSignedIn: boolean;
  user?: any;
  error?: string;
  provider?: OAuthProvider;
}

/**
 * OAuth redirection configuration
 */
export interface OAuthRedirectConfig {
  provider: OAuthProvider;
  redirectUri?: string;
  state?: string;
  scope?: string[];
  customParams?: Record<string, string>;
}

/**
 * OAuth loader event types
 */
export type OAuthLoaderEvent = 
  | "oauth_started"
  | "oauth_redirecting" 
  | "oauth_completed"
  | "oauth_cancelled"
  | "oauth_error";

export interface OAuthLoaderEventData {
  event: OAuthLoaderEvent;
  provider: OAuthProvider;
  timestamp: number;
  data?: any;
  error?: string;
}

/**
 * Hook return type for OAuth loader management
 */
export interface UseOAuthLoaderReturn {
  isLoading: boolean;
  provider: OAuthProvider;
  startOAuth: (provider: OAuthProvider, config?: Partial<OAuthRedirectConfig>) => Promise<void>;
  cancelOAuth: () => void;
  error: string | null;
  clearError: () => void;
}
